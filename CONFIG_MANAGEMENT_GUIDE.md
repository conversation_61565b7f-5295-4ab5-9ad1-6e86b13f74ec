# 配置统一管理指南

## 概述

本指南介绍了直播服务的统一配置管理系统，该系统整合了所有分散的配置类，提供了统一的配置访问接口和管理功能。

## 主要特性

- ✅ **统一配置**: 所有配置集中在 `UnifiedLiveConfig` 中
- ✅ **配置验证**: 启动时自动验证配置的有效性
- ✅ **配置缓存**: 提供高性能的配置访问
- ✅ **配置监控**: 实时监控配置健康状态
- ✅ **配置迁移**: 自动从旧配置迁移到新配置
- ✅ **兼容适配**: 为旧代码提供兼容性支持
- ✅ **变更监听**: 配置变更时自动触发相应处理

## 配置结构

```yaml
ydwl:
  live:
    # 推流配置
    stream:
      push-domain: push.live.ycyyx.com
      play-domain: play.live.ycyyx.com
      app-name: live
      push-secret-key: your-secret-key
      play-secret-key: your-secret-key
      play-url-expire-seconds: 86400
      push-timeout: 300
    
    # 任务配置
    task:
      enabled: true
      status-check-interval: 60
      record-check-interval: 120
    
    # 认证配置
    auth:
      token-timeout: 86400
      access-control-enabled: true
    
    # 视频处理配置
    video:
      max-file-size: 2048
      supported-formats: ["mp4", "flv", "m3u8", "mov"]
      max-retry-count: 3
      retry-interval: 300
      transcode-timeout: 7200
      async-transcode: true
      transcode-queue-size: 100
      transcode-thread-pool-size: 5
    
    # 缓存配置
    cache:
      enabled: true
      live-info-cache-time: 300
      stream-info-cache-time: 600
      user-info-cache-time: 1800
      default-ttl-minutes: 10
      cache-null-values: false
    
    # OSS配置
    oss:
      access-key: ${ALIYUN_OSS_ACCESS_KEY:}
      secret-key: ${ALIYUN_OSS_SECRET_KEY:}
      endpoint: ${ALIYUN_OSS_ENDPOINT:}
      bucket-name: ${ALIYUN_OSS_BUCKET:}
      max-file-size: 2147483648
    
    # 阿里云配置
    aliyun:
      access-key-id: ${ALIYUN_ACCESS_KEY_ID:}
      access-key-secret: ${ALIYUN_ACCESS_KEY_SECRET:}
      region-id: ${ALIYUN_REGION_ID:cn-beijing}
      domain-name: ${ALIYUN_LIVE_DOMAIN:}
    
    # 微信小程序配置
    wx-miniapp:
      appid: ${WX_MINIAPP_APPID:}
      secret: ${WX_MINIAPP_SECRET:}
      token: ${WX_MINIAPP_TOKEN:}
      aes-key: ${WX_MINIAPP_AES_KEY:}
      msg-data-format: JSON
```

## 使用方式

### 1. 注入配置管理器

```java
@Service
@RequiredArgsConstructor
public class YourService {
    
    private final ConfigManager configManager;
    
    public void someMethod() {
        // 获取推流配置
        UnifiedLiveConfig.StreamConfig streamConfig = configManager.getStreamConfig();
        String pushDomain = streamConfig.getPushDomain();
        
        // 获取视频处理配置
        UnifiedLiveConfig.VideoConfig videoConfig = configManager.getVideoConfig();
        long maxFileSize = videoConfig.getMaxFileSize();
    }
}
```

### 2. 直接注入统一配置

```java
@Service
@RequiredArgsConstructor
public class YourService {
    
    private final UnifiedLiveConfig liveConfig;
    
    public void someMethod() {
        // 直接访问配置
        String pushDomain = liveConfig.getStream().getPushDomain();
        boolean cacheEnabled = liveConfig.getCache().isEnabled();
    }
}
```

### 3. 兼容旧代码（推荐逐步迁移）

```java
@Service
@RequiredArgsConstructor
public class YourService {
    
    // 旧的配置类仍然可以使用（通过适配器提供）
    private final LiveStreamConfig streamConfig;
    private final VideoProcessConfig videoConfig;
    
    public void someMethod() {
        // 旧代码无需修改
        String pushDomain = streamConfig.getPushDomain();
        long maxFileSize = videoConfig.getMaxFileSize();
    }
}
```

## 配置管理功能

### 1. 配置监控

访问配置健康检查端点：
```
GET /live/config/health
```

### 2. 配置查看

```
GET /live/config/all        # 获取所有配置
GET /live/config/stream     # 获取推流配置
GET /live/config/video      # 获取视频处理配置
GET /live/config/cache      # 获取缓存配置
```

### 3. 配置更新

```
POST /live/config/stream    # 更新推流配置
POST /live/config/cache     # 更新缓存配置
```

### 4. 配置迁移

启用配置迁移：
```yaml
ydwl:
  live:
    config:
      migration:
        enabled: true
```

获取迁移指南：
```
GET /live/config/migration-guide
```

## 环境变量支持

配置支持通过环境变量进行设置，特别适合容器化部署：

```bash
# 阿里云配置
export ALIYUN_ACCESS_KEY_ID=your-access-key-id
export ALIYUN_ACCESS_KEY_SECRET=your-access-key-secret
export ALIYUN_REGION_ID=cn-beijing

# OSS配置
export ALIYUN_OSS_ACCESS_KEY=your-oss-access-key
export ALIYUN_OSS_SECRET_KEY=your-oss-secret-key
export ALIYUN_OSS_ENDPOINT=oss-cn-beijing.aliyuncs.com
export ALIYUN_OSS_BUCKET=your-bucket-name

# 微信小程序配置
export WX_MINIAPP_APPID=your-miniapp-appid
export WX_MINIAPP_SECRET=your-miniapp-secret
```

## 迁移步骤

### 1. 启用配置迁移

在 `application.yml` 中设置：
```yaml
ydwl:
  live:
    config:
      migration:
        enabled: true
```

### 2. 重启应用

重启后系统会自动检测旧配置并迁移到新的统一配置中。

### 3. 验证配置

通过配置管理端点验证配置是否正确迁移：
```
GET /live/config/health
```

### 4. 更新代码（可选）

逐步将代码中的旧配置类替换为新的配置管理器：

```java
// 旧方式
@Autowired
private LiveStreamConfig streamConfig;

// 新方式
@Autowired
private ConfigManager configManager;
// 或
@Autowired
private UnifiedLiveConfig liveConfig;
```

### 5. 清理旧配置

确认迁移成功后，可以删除旧的配置类和配置项。

## 最佳实践

1. **使用环境变量**: 敏感信息（如密钥）通过环境变量设置
2. **配置验证**: 启动时会自动验证配置，确保及时发现问题
3. **配置监控**: 定期检查配置健康状态
4. **渐进迁移**: 可以逐步从旧配置迁移到新配置
5. **权限控制**: 配置管理端点需要管理员权限

## 故障排除

### 1. 配置验证失败

检查配置项是否正确设置，特别是必填项。

### 2. 配置迁移失败

确保旧配置类存在且配置正确，检查迁移日志。

### 3. 配置更新不生效

检查是否有配置缓存，可能需要重启应用。

### 4. 权限不足

确保用户具有管理员权限才能访问配置管理端点。

## 注意事项

1. 配置管理端点默认关闭，需要手动启用
2. 配置迁移功能仅在迁移期间启用
3. 敏感配置信息不会在日志中显示
4. 配置变更会触发相应的事件处理
5. 建议在生产环境中谨慎使用配置更新功能
