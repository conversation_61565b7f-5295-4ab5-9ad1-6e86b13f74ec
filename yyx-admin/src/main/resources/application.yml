# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8080
  servlet:
    # 应用的访问路径
    context-path: /
  # undertow 配置
  undertow:
    # HTTP post内容的最大大小。当值为-1时，默认值为大小是无限的
    max-http-post-size: -1
    # 以下的配置会影响buffer,这些buffer会用于服务器连接的IO操作,有点类似netty的池化内存管理
    # 每块buffer的空间大小,越小的空间被利用越充分
    buffer-size: 512
    # 是否分配的直接内存
    direct-buffers: true
    threads:
      # 设置IO线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认设置每个CPU核心一个线程
      io: 8
      # 阻塞任务线程池, 当执行类似servlet请求阻塞操作, undertow会从这个线程池中取得线程,它的值设置取决于系统的负载
      worker: 256

captcha:
  # 是否启用验证码校验
  enable: true
  # 验证码类型 math 数组计算 char 字符验证
  type: MATH
  # line 线段干扰 circle 圆圈干扰 shear 扭曲干扰
  category: CIRCLE
  # 数字验证码位数
  numberLength: 1
  # 字符验证码长度
  charLength: 4

# 日志配置
logging:
  level:
    com.ydwl: @logging.level@
    org.springframework: warn
    org.mybatis.spring.mapper: error
    org.apache.fury: warn
  config: classpath:logback-plus.xml

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  application:
    name: ydwl-mg
  threads:
    # 开启虚拟线程 仅jdk21可用
    virtual:
      enabled: false
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: @profiles.active@
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 10MB
      # 设置总上传的文件大小
      max-request-size: 20MB
  mvc:
    # 设置静态资源路径 防止所有请求都去查静态资源
    static-path-pattern: /static/**
    format:
      date-time: yyyy-MM-dd HH:mm:ss
  jackson:
    # 日期格式化
    date-format: yyyy-MM-dd HH:mm:ss
    serialization:
      # 格式化输出
      indent_output: false
      # 忽略无法转换的对象
      fail_on_empty_beans: false
    deserialization:
      # 允许对象忽略json中不存在的属性
      fail_on_unknown_properties: false

# Sa-Token配置
sa-token:
  # token名称 (同时也是cookie名称)
  token-name: Authorization
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: false
  # jwt秘钥
  jwt-secret-key: abcdefghijklmnopqrstuvwxyz

# security配置
security:
  # 排除路径
  excludes:
    - /*.html
    - /**/*.html
    - /**/*.css
    - /**/*.js
    - /favicon.ico
    - /error
    - /*/api-docs
    - /*/api-docs/**
    - /app/*/*/**
    - /live/replay/test
    - /admin/api/v1/**
    - /live/manage/**
    - /live/transcode/**
    - /live/videoUpload/ossCallback

# 多租户配置
tenant:
  # 是否开启
  enable: true
  # 排除表
  excludes:
    - sys_menu
    - sys_tenant
    - sys_tenant_package
    - sys_role_dept
    - sys_role_menu
    - sys_user_post
    - sys_user_role
    - sys_client
    - sys_oss_config

# MyBatisPlus配置
# https://baomidou.com/config/
mybatis-plus:
  # 自定义配置 是否全局开启逻辑删除 关闭后 所有逻辑删除功能将失效
  enableLogicDelete: true
  # 多包名使用 例如 com.ydwl.**.mapper,org.xxx.**.mapper
  mapperPackage: com.ydwl.**.mapper
  # 对应的 XML 文件位置
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.ydwl.**.domain
  global-config:
    dbConfig:
      # 主键类型
      # AUTO 自增 NONE 空 INPUT 用户输入 ASSIGN_ID 雪花 ASSIGN_UUID 唯一 UUID
      # 如需改为自增 需要将数据库表全部设置为自增
      idType: ASSIGN_ID

# 数据加密
mybatis-encryptor:
  # 是否开启加密
  enable: false
  # 默认加密算法
  algorithm: BASE64
  # 编码方式 BASE64/HEX。默认BASE64
  encode: BASE64
  # 安全秘钥 对称算法的秘钥 如：AES，SM4
  password:
  # 公私钥 非对称算法的公私钥 如：SM2，RSA
  publicKey:
  privateKey:

# api接口加密
api-decrypt:
  # 是否开启全局接口加密
  enabled: false
  # AES 加密头标识
  headerFlag: encrypt-key
  # 响应加密公钥 非对称算法的公私钥 如：SM2，RSA 使用者请自行更换
  # 对应前端解密私钥 MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAmc3CuPiGL/LcIIm7zryCEIbl1SPzBkr75E2VMtxegyZ1lYRD+7TZGAPkvIsBcaMs6Nsy0L78n2qh+lIZMpLH8wIDAQABAkEAk82Mhz0tlv6IVCyIcw/s3f0E+WLmtPFyR9/WtV3Y5aaejUkU60JpX4m5xNR2VaqOLTZAYjW8Wy0aXr3zYIhhQQIhAMfqR9oFdYw1J9SsNc+CrhugAvKTi0+BF6VoL6psWhvbAiEAxPPNTmrkmrXwdm/pQQu3UOQmc2vCZ5tiKpW10CgJi8kCIFGkL6utxw93Ncj4exE/gPLvKcT+1Emnoox+O9kRXss5AiAMtYLJDaLEzPrAWcZeeSgSIzbL+ecokmFKSDDcRske6QIgSMkHedwND1olF8vlKsJUGK3BcdtM8w4Xq7BpSBwsloE=
  publicKey: MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAJnNwrj4hi/y3CCJu868ghCG5dUj8wZK++RNlTLcXoMmdZWEQ/u02RgD5LyLAXGjLOjbMtC+/J9qofpSGTKSx/MCAwEAAQ==
  # 请求解密私钥 非对称算法的公私钥 如：SM2，RSA 使用者请自行更换
  # 对应前端加密公钥 MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKoR8mX0rGKLqzcWmOzbfj64K8ZIgOdHnzkXSOVOZbFu/TJhZ7rFAN+eaGkl3C4buccQd/EjEsj9ir7ijT7h96MCAwEAAQ==
  privateKey: MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAqhHyZfSsYourNxaY7Nt+PrgrxkiA50efORdI5U5lsW79MmFnusUA355oaSXcLhu5xxB38SMSyP2KvuKNPuH3owIDAQABAkAfoiLyL+Z4lf4Myxk6xUDgLaWGximj20CUf+5BKKnlrK+Ed8gAkM0HqoTt2UZwA5E2MzS4EI2gjfQhz5X28uqxAiEA3wNFxfrCZlSZHb0gn2zDpWowcSxQAgiCstxGUoOqlW8CIQDDOerGKH5OmCJ4Z21v+F25WaHYPxCFMvwxpcw99EcvDQIgIdhDTIqD2jfYjPTY8Jj3EDGPbH2HHuffvflECt3Ek60CIQCFRlCkHpi7hthhYhovyloRYsM+IS9h/0BzlEAuO0ktMQIgSPT3aFAgJYwKpqRYKlLDVcflZFCKY7u3UP8iWi1Qw0Y=

springdoc:
  api-docs:
    # 是否开启接口文档
    enabled: true
  info:
    # 标题
    title: '标题：管理系统_接口文档'
    # 描述
    description: '描述：用于管理集团旗下公司的人员信息,具体包括XXX,XXX模块...'
    # 版本
    version: '版本号: 0.1'
    # 作者信息
    contact:
      name: Lin Yi
  components:
    # 鉴权方式配置
    security-schemes:
      apiKey:
        type: APIKEY
        in: HEADER
  #这里定义了两个分组，可定义多个，也可以不定义
  group-configs:
    - group: 1.演示模块
      packages-to-scan: com.ydwl.demo
    - group: 2.通用模块
      packages-to-scan: com.ydwl.web
    - group: 3.系统模块
      packages-to-scan: com.ydwl.system
    - group: 4.代码生成模块
      packages-to-scan: com.ydwl.generator
    - group: 5.直播模块
      packages-to-scan: com.ydwl.live

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludeUrls:
    - /system/notice

# 全局线程池相关配置
thread-pool:
  # 是否开启线程池
  enabled: false
  # 队列最大长度
  queueCapacity: 128
  # 线程池维护线程所允许的空闲时间
  keepAliveSeconds: 300

--- # 分布式锁 lock4j 全局配置
lock4j:
  # 获取分布式锁超时时间，默认为 3000 毫秒
  acquire-timeout: 3000
  # 分布式锁的超时时间，默认为 30 秒
  expire: 30000

--- # Actuator 监控端点的配置项
management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: ALWAYS
    logfile:
      external-file: ./logs/sys-console.log

--- # 默认/推荐使用sse推送
sse:
  enabled: true
  path: /resource/sse

--- # websocket
websocket:
  # 如果关闭 需要和前端开关一起关闭
  enabled: false
  # 路径
  path: /resource/websocket
  # 设置访问源地址
  allowedOrigins: '*'
oss:
  endpoint: oss-cn-beijing.aliyuncs.com
  domain: "https://mts.cn-beijing.aliyuncs.com"
  accessKey: LTAI5tGA15ASheoXwvc7pYcN
  secretKey: ******************************
  bucketName: video-ydwl
  region: "cn-bei jing"
  isHttps: true

--- # 阿里云配置
ydwl:
  # 直播模块配置
  live:
    # 推流配置
    stream:
      push-domain: push.live.ycyyx.com
      play-domain: play.live.ycyyx.com
      app-name: live
      push-secret-key: 2p5n2R8aA6tTQtu2
      play-secret-key: LznJujBX81mOJM7E
      play-url-expire-seconds: 86400
      push-timeout: 300

    # 任务配置
    task:
      enabled: true
      status-check-interval: 60
      record-check-interval: 120

    # 认证配置
    auth:
      token-timeout: 86400
      access-control-enabled: true

    # 视频处理配置
    video:
      max-file-size: 2048
      supported-formats: ["mp4", "flv", "m3u8", "mov"]
      max-retry-count: 3
      retry-interval: 300
      transcode-timeout: 7200
      async-transcode: true
      transcode-queue-size: 100
      transcode-thread-pool-size: 5

    # 缓存配置
    cache:
      enabled: true
      live-info-cache-time: 300
      stream-info-cache-time: 600
      user-info-cache-time: 1800
      default-ttl-minutes: 10
      cache-null-values: false

    # OSS配置
    oss:
      access-key: LTAI5tGA15ASheoXwvc7pYcN
      secret-key: ******************************
      endpoint: oss-cn-beijing.aliyuncs.com
      bucket-name: video-ydwl
      notify-topic: ""
      callback-url: https://yd-live.cpolar.cn/live/videoUpload/ossCallback
      max-file-size: 2147483648
      cdn-domain: mps.play.ycyyx.com

    # 阿里云配置
    aliyun:
      access-key-id: LTAI5tGA15ASheoXwvc7pYcN
      access-key-secret: ******************************
      region-id: cn-beijing
      domain-name: play.live.ycyyx.com
      oss-endpoint: oss-cn-beijing.aliyuncs.com
      oss-bucket: ydwl-live

    # 微信小程序配置
    wx-miniapp:
      appid: 你的appid
      secret: 你的app secret
      token: 你的token
      aes-key: 你的EncodingAESKey
      msg-data-format: JSON
  # 转码模块配置
  transcoding:
    # OSS配置
    oss:
      endpoint: oss-cn-beijing.aliyuncs.com
      access-key-id: LTAI5tGA15ASheoXwvc7pYcN
      access-key-secret: ******************************
      bucket-name: video-ydwl
      cdn-domain: mps.play.ycyyx.com
      callback-domain: mps.play.ycyyx.com
      callback-url: https://yd-live.cpolar.cn/live/videoUpload/ossCallback
      max-file-size: 2147483648

    # 阿里云配置
    aliyun:
      access-key-id: LTAI5tGA15ASheoXwvc7pYcN
      access-key-secret: ******************************
      region-id: cn-beijing
      domain-name: play.live.ycyyx.com
      oss-endpoint: oss-cn-beijing.aliyuncs.com
      oss-bucket: ydwl-live

    # 转码配置
    transcode:
      # FC转码配置
      fc:
        access-key-id: LTAI5tGA15ASheoXwvc7pYcN
        access-key-secret: ******************************
        oss-endpoint: oss-cn-beijing.aliyuncs.com
        oss-bucket-name: video-ydwl
        fc-endpoint: cn-beijing.fc.aliyuncs.com
        transcode-complete-callback: https://test.com/typeAliasesPackag
        pipeline: default-pipeline
        function-name: video-turncode-ydwl-prod
        uhd480-p: FC-480p-template
        sd720-p: FC-720p-template
        hd1080-p: FC-1080p-template

      # MTS转码配置
      mts:
        access-key-id: LTAI5tGA15ASheoXwvc7pYcN
        access-key-secret: ******************************
        oss-endpoint: oss-cn-beijing.aliyuncs.com
        oss-bucket-name: video-ydwl
        mts-endpoint: mts.cn-beijing.aliyuncs.com
        pipeline: 654b344676a0456dbb6c48b1c44428bb
        transcode-complete-callback: https://test.com/typeAliasesPackag
        uhd480-p: 78b4d8f80f5c44a5b23eb07c1f585bb3
        sd720-p: c52a2435a9004399a2c8b1904608cbbe
        hd1080-p: 752c184e452a41db86714fdf74601e2b
        hd2k: ""
        location: oss-cn-beijing
        large: 524288000
        medium: 104857600

      # 文件大小阈值配置
      file-threshold:
        large: 524288000
        medium: 104857600

      # 回调配置
      callback:
        default-url: https://test.com/typeAliasesPackag
        fc-url: https://test.com/typeAliasesPackag
        mts-url: https://test.com/typeAliasesPackag

    # 直播流配置
    live-stream:
      push-domain: push.live.ycyyx.com
      play-domain: play.live.ycyyx.com
      app-name: live
      push-secret-key: 2p5n2R8aA6tTQtu2
      play-secret-key: LznJujBX81mOJM7E
      play-url-expire-seconds: 86400
      live-start-callback: https://test.com/typeAliasesPackag
      live-stop-callback: https://test.com/typeAliasesPackag
      live-record-callback: https://test.com/typeAliasesPackag
      live-record-complete-callback: https://test.com/typeAliasesPackag
