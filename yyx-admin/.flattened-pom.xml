<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.ydwl</groupId>
    <artifactId>ydwl-live</artifactId>
    <version>5.4.0</version>
  </parent>
  <groupId>com.ydwl</groupId>
  <artifactId>yyx-admin</artifactId>
  <version>5.4.0</version>
  <description>web服务入口</description>
  <dependencies>
    <dependency>
      <groupId>com.mysql</groupId>
      <artifactId>mysql-connector-j</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-doc</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-social</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-ratelimiter</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-mail</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-system</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-job</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-generator</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-demo</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-transcoding</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-live</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>de.codecentric</groupId>
      <artifactId>spring-boot-admin-starter-client</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>
  <build>
    <finalName>${project.artifactId}</finalName>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <version>${spring-boot.version}</version>
        <executions>
          <execution>
            <goals>
              <goal>repackage</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <version>${maven-jar-plugin.version}</version>
      </plugin>
      <plugin>
        <artifactId>maven-war-plugin</artifactId>
        <version>${maven-war-plugin.version}</version>
        <configuration>
          <failOnMissingWebXml>false</failOnMissingWebXml>
          <warName>${project.artifactId}</warName>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
