<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.ydwl</groupId>
    <artifactId>yyx-modules</artifactId>
    <version>5.4.0</version>
  </parent>
  <groupId>com.ydwl</groupId>
  <artifactId>yyx-transcoding</artifactId>
  <version>5.4.0</version>
  <description>转码模块</description>
  <dependencies>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-core</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-doc</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-sms</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-mail</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-redis</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-idempotent</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-mybatis</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-log</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-excel</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-security</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-web</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-ratelimiter</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-translation</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-sensitive</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-encrypt</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-tenant</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-websocket</artifactId>
    </dependency>
    <dependency>
      <groupId>com.aliyun</groupId>
      <artifactId>aliyun-java-sdk-live</artifactId>
      <version>3.9.65</version>
    </dependency>
    <dependency>
      <groupId>com.aliyun</groupId>
      <artifactId>aliyun-java-sdk-core</artifactId>
      <version>4.6.1</version>
    </dependency>
    <dependency>
      <groupId>com.aliyun</groupId>
      <artifactId>mts20140618</artifactId>
      <version>6.0.0</version>
    </dependency>
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>fastjson</artifactId>
      <version>${fastjson.version}</version>
    </dependency>
    <dependency>
      <groupId>com.aliyun</groupId>
      <artifactId>fc20230330</artifactId>
      <version>4.1.0</version>
    </dependency>
    <dependency>
      <groupId>com.aliyun.oss</groupId>
      <artifactId>aliyun-sdk-oss</artifactId>
      <version>3.17.4</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.retry</groupId>
      <artifactId>spring-retry</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-aspects</artifactId>
    </dependency>
  </dependencies>
  <repositories>
    <repository>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>true</enabled>
      </snapshots>
      <id>sonatype-nexus-staging</id>
      <name>Sonatype Nexus Staging</name>
      <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
  </repositories>
  <build />
</project>
