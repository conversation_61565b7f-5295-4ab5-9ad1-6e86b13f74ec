package com.ydwl.LiveTranscoding.service.impl;

import com.ydwl.LiveTranscoding.config.TranscodingConfigManager;
import com.ydwl.common.core.domain.dto.transcode.TranscodeInitResponseVo;
import com.ydwl.common.core.domain.dto.transcode.TranscodeRequestVo;
import com.ydwl.common.core.domain.dto.transcode.TranscodeTemplateInfo;
import com.ydwl.common.core.service.IUniversalTranscodeService;
import com.ydwl.LiveTranscoding.service.FcTranscodeService;
import com.ydwl.LiveTranscoding.service.MctTranscodeService;
import com.ydwl.LiveTranscoding.service.TranscodeErrorHandler;
import com.ydwl.LiveTranscoding.service.TranscodeTemplateService;
import com.ydwl.LiveTranscoding.domain.dto.FcVideoFcTranscodeDto;
import com.ydwl.LiveTranscoding.domain.dto.MtsTranscodeRequestDto;
import com.ydwl.LiveTranscoding.domain.dto.MtsOutputItemDto;
import com.ydwl.LiveTranscoding.domain.dto.MtsInput;
import com.ydwl.LiveTranscoding.exception.TranscodingException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 统一转码服务实现类
 * 整合FC和MTS转码，提供一致的API接口
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UniversalTranscodeServiceImpl implements IUniversalTranscodeService {

    private final FcTranscodeService fcTranscodeService;
    private final MctTranscodeService mctTranscodeService;
    private final TranscodingConfigManager configManager;
    private final TranscodeTemplateService templateService;
    private final TranscodeErrorHandler errorHandler;

    @Override
    public TranscodeInitResponseVo transcode(TranscodeRequestVo request) {
        log.info("开始统一转码处理，BizId: {}, 文件: {}/{}",
                request.getBizId(), request.getBucket(), request.getObject());

        request.validate();

        // 如果没有提供转码模板，使用智能推荐
        if (CollectionUtils.isEmpty(request.getTemplates())) {
            log.info("未提供转码模板，使用智能推荐，BizId: {}, 文件大小: {} bytes",
                    request.getBizId(), request.getFileSize());

            try {
                // 预选转码方法用于模板推荐
                String preselectedMethod = selectTranscodeMethod(request);
                List<String> recommendedTemplateIds = templateService.recommendTemplates(
                    request.getFileSize(), preselectedMethod);

                if (recommendedTemplateIds.isEmpty()) {
                    log.warn("智能推荐未返回模板，使用默认模板，BizId: {}", request.getBizId());
                    recommendedTemplateIds = templateService.getDefaultTemplates(preselectedMethod);
                }

                log.info("推荐模板，BizId: {}, 方法: {}, 模板: {}",
                        request.getBizId(), preselectedMethod, recommendedTemplateIds);

                // 将模板ID转换为TranscodeTemplateInfo（这里简化处理）
                List<TranscodeTemplateInfo> defaultTemplates = recommendedTemplateIds.stream()
                    .map(this::createTemplateInfoFromId)
                    .collect(Collectors.toList());

                request.setTemplates(defaultTemplates);

            } catch (Exception e) {
                log.error("智能推荐模板失败，使用默认720p+1080p，BizId: {}", request.getBizId(), e);
                request.setTemplates(createDefaultTemplates());
            }
        }

        String method = selectTranscodeMethod(request);
        log.info("选择转码方式: {}, BizId: {}", method, request.getBizId());

        return transcodeWithMethod(request, method);
    }

    @Override
    public TranscodeInitResponseVo transcodeWithMethod(TranscodeRequestVo request, String method) {
        log.info("使用指定方式转码，方式: {}, BizId: {}", method, request.getBizId());
        request.validate();
        return switch (method.toLowerCase()) {
            case "fc" -> transcodeWithFc(request);
            case "mts" -> transcodeWithMts(request);
            default -> throw new IllegalArgumentException("不支持的转码方式: " + method);
        };
    }

    private String selectTranscodeMethod(TranscodeRequestVo request) {
        if (request.isForceMethod()) {
            String method = request.getForceMethod();
            log.info("强制使用转码方式: {}, BizId: {}", method, request.getBizId());
            return method;
        }

        Long fileSize = request.getFileSize();
        if (fileSize != null) {
            if (fileSize > configManager.getMtsConfig().getLarge()) {
                log.info("大文件({} MB)使用MTS转码, BizId: {}", fileSize / 1024 / 1024, request.getBizId());
                return "mts";
            } else {
                log.info("中小文件({} MB)使用FC转码, BizId: {}", fileSize / 1024 / 1024, request.getBizId());
                return "fc";
            }
        }

        if (request.getTemplates().size() > 3) {
            log.info("输出3个清晰度-使用MTS, BizId: {}", request.getBizId());
            return "mts";
        }

        log.info("默认使用FC转码, BizId: {}", request.getBizId());
        return "fc";
    }

    private TranscodeInitResponseVo transcodeWithFc(TranscodeRequestVo request) {
        log.info("开始FC转码，BizId: {}", request.getBizId());
        try {
            FcVideoFcTranscodeDto fcRequest = buildFcRequest(request);
            return fcTranscodeService.transcode(
                fcRequest.getInput(),
                fcRequest.getOutputs(),
                fcRequest.getUserData(),
                fcRequest.getCallbackUrl(),
                fcRequest.getPipelineId()
            );
        } catch (Exception e) {
            log.error("FC转码调用失败，BizId: {}", request.getBizId(), e);
            throw new TranscodingException("FC转码调用失败", request.getBizId(), "fc", e);
        }
    }

    private TranscodeInitResponseVo transcodeWithMts(TranscodeRequestVo request) {
        log.info("开始MTS转码，BizId: {}", request.getBizId());
        try {
            MtsTranscodeRequestDto mtsRequest = buildMtsRequest(request);
            return mctTranscodeService.MtsTranscode(mtsRequest);
        } catch (Exception e) {
            log.error("MTS转码调用失败，BizId: {}", request.getBizId(), e);
            throw new TranscodingException("MTS转码调用失败", request.getBizId(), "mts", e);
        }
    }

    private FcVideoFcTranscodeDto buildFcRequest(TranscodeRequestVo request) {
        FcVideoFcTranscodeDto fcRequest = new FcVideoFcTranscodeDto();
        FcVideoFcTranscodeDto.InputDTO input = new FcVideoFcTranscodeDto.InputDTO();
        input.setBucket(request.getBucket());
        input.setLocation(StringUtils.hasText(request.getInputLocation()) ? request.getInputLocation() : TranscodeRequestVo.DEFAULT_INPUT_LOCATION);
        input.setObject(request.getObject());
        fcRequest.setInput(input);

        fcRequest.setOutputs(buildFcOutputs(request));
        fcRequest.setUserData(request.getBizId());
        String effectiveCallbackUrl = StringUtils.hasText(request.getCallbackUrl()) ? request.getCallbackUrl() : configManager.getFcConfig().getTranscodeCompleteCallback();
        fcRequest.setCallbackUrl(effectiveCallbackUrl);
        fcRequest.setPipelineId(StringUtils.hasText(request.getPipelineId()) ? request.getPipelineId() : configManager.getFcConfig().getPipeline());
        fcRequest.setSegmentTime(request.getSegmentTime());
        fcRequest.setEmergencyMode(request.getEmergencyMode());
        fcRequest.setTimeout(request.getTimeout());
        fcRequest.setSendStartCallback(request.getSendStartCallback());
        return fcRequest;
    }

    private List<FcVideoFcTranscodeDto.OutputDTO> buildFcOutputs(TranscodeRequestVo request) {
        String basePath = extractBasePath(request.getObject());
        String fileName = extractFileNameWithoutExtension(request.getObject());

        return request.getTemplates().stream().map(template -> {
            String outputObject = String.format("%s/%s_%s/playlist.m3u8", basePath, fileName, template.getName());

            // 使用模板映射获取实际的模板ID
            String templateName = template.getName();
            String templateId = mapFcTemplateId(templateName);

            return new FcVideoFcTranscodeDto.OutputDTO(outputObject, templateId, request.getBizId(), null);
        }).collect(Collectors.toList());
    }

    /**
     * 映射模板名称到FC模板ID
     *
     * @param templateName 模板名称
     * @return FC模板ID
     */
    private String mapFcTemplateId(String templateName) {
        // 1. 首先尝试从数据库中获取外部ID
        String externalId = getExternalTemplateIdFromDb(templateName, "fc");
            log.info("从数据库获取FC模板映射: {} -> {}", templateName, externalId);
            return externalId;
    }

    private MtsTranscodeRequestDto buildMtsRequest(TranscodeRequestVo request) {
        MtsTranscodeRequestDto mtsRequest = new MtsTranscodeRequestDto();

        // 设置输入
        MtsInput input = new MtsInput();
        input.setBucket(request.getBucket());
        input.setLocation(StringUtils.hasText(request.getInputLocation()) ? request.getInputLocation() : TranscodeRequestVo.DEFAULT_INPUT_LOCATION);
        input.setObject(request.getObject());
        mtsRequest.setInput(input);
        // 设置业务ID
        mtsRequest.setBizId(request.getBizId());
        // 设置输出配置
        mtsRequest.setOutputs(buildMtsOutputs(request));
        // 设置其他参数
        mtsRequest.setOutputBucket(request.getEffectiveOutputBucket());
        mtsRequest.setOutputLocation(request.getOutputLocation());
        mtsRequest.setPipelineId(StringUtils.hasText(request.getPipelineId()) ? request.getPipelineId() : configManager.getMtsConfig().getPipeline());
        // 设置回调URL
        String effectiveCallbackUrl = StringUtils.hasText(request.getCallbackUrl()) ? request.getCallbackUrl() : configManager.getMtsConfig().getTranscodeCompleteCallback();
        mtsRequest.setCallbackUrl(effectiveCallbackUrl);
        return mtsRequest;
    }

    private List<MtsOutputItemDto> buildMtsOutputs(TranscodeRequestVo request) {
        String fileName = extractFileNameWithoutExtension(request.getObject());
        return request.getTemplates().stream().map(template -> {
            String outputObject = String.format("%s_%s/playlist.m3u8", fileName, template.getName());

            // 使用模板映射获取实际的模板ID
            String templateName = template.getName();
            String templateId = mapMtsTemplateId(templateName);

            MtsOutputItemDto output = new MtsOutputItemDto();
            output.setTemplateId(templateId);
            output.setOutputObject(outputObject);
            output.setUserData(request.getBizId());
            return output;
        }).collect(Collectors.toList());
    }

    /**
     * 映射模板名称到MTS模板ID
     *
     * @param templateName 模板名称
     * @return MTS模板ID
     */
    private String mapMtsTemplateId(String templateName) {
        return switch (templateName.toLowerCase()) {
            case "480p" -> configManager.getMtsConfig().getUhd480P();
            case "720p" -> configManager.getMtsConfig().getSd720P();
            case "1080p" -> configManager.getMtsConfig().getHd1080P();
            default -> templateName;
        };
    }

    /**
     * 从数据库获取外部模板ID
     *
     * @param templateName 模板名称
     * @param templateType 模板类型 (fc 或 mts)
     * @return 外部模板ID，如果未找到则返回null
     */
    private String getExternalTemplateIdFromDb(String templateName, String templateType) {
    return "test";
    }

    private String extractBasePath(String objectPath) {
        if (objectPath == null || !objectPath.contains("/")) {
            return "";
        }
        return objectPath.substring(0, objectPath.lastIndexOf('/'));
    }

    private String extractFileName(String objectPath) {
        if (objectPath == null) {
            return "";
        }
        if (!objectPath.contains("/")) {
            return objectPath;
        }
        return objectPath.substring(objectPath.lastIndexOf('/') + 1);
    }

    private String extractFileNameWithoutExtension(String objectPath) {
        String fileName = extractFileName(objectPath);
        if (fileName.contains(".")) {
            return fileName.substring(0, fileName.lastIndexOf('.'));
        }
        return fileName;
    }

    /**
     * 根据模板ID创建TranscodeTemplateInfo（简化实现）
     */
    private TranscodeTemplateInfo createTemplateInfoFromId(String templateId) {
        TranscodeTemplateInfo template = new TranscodeTemplateInfo();
        template.setName(templateId);
        template.setContainer("m3u8");
        template.setVideoCodec("H264");
        template.setAudioCodec("AAC");

        // 根据模板ID设置基本参数
        switch (templateId.toLowerCase()) {
            case "480p" -> {
                template.setVideoWidth(854);
                template.setVideoHeight(480);
                template.setVideoBitrate(1500);
                template.setAudioBitrate(128);
            }
            case "720p" -> {
                template.setVideoWidth(1280);
                template.setVideoHeight(720);
                template.setVideoBitrate(2500);
                template.setAudioBitrate(128);
            }
            case "1080p" -> {
                template.setVideoWidth(1920);
                template.setVideoHeight(1080);
                template.setVideoBitrate(4000);
                template.setAudioBitrate(192);
            }
            case "2k" -> {
                template.setVideoWidth(2560);
                template.setVideoHeight(1440);
                template.setVideoBitrate(6000);
                template.setAudioBitrate(192);
            }
            case "4k" -> {
                template.setVideoWidth(3840);
                template.setVideoHeight(2160);
                template.setVideoBitrate(12000);
                template.setAudioBitrate(256);
            }
            default -> {
                // 默认720p
                template.setVideoWidth(1280);
                template.setVideoHeight(720);
                template.setVideoBitrate(2500);
                template.setAudioBitrate(128);
            }
        }

        template.setVideoFramerate(25);
        template.setAudioSamplerate(44100);
        template.setAudioChannels(2);

        return template;
    }

    /**
     * 创建默认模板列表（720p + 1080p）
     */
    private List<TranscodeTemplateInfo> createDefaultTemplates() {
        List<TranscodeTemplateInfo> templates = new ArrayList<>();
        templates.add(createTemplateInfoFromId("720p"));
        templates.add(createTemplateInfoFromId("1080p"));
        return templates;
    }
}
