package com.ydwl.LiveTranscoding.config;

import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * 配置适配器Bean
 * 为现有代码提供兼容的GlobalConfigProperties Bean
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Configuration
@RequiredArgsConstructor
public class ConfigAdapterBean {

    private final TranscodingConfigManager configManager;

    /**
     * 提供兼容的GlobalConfigProperties Bean
     * 将新的统一配置转换为旧的GlobalConfigProperties格式
     */
    @Bean
    @Primary
    public GlobalConfigProperties globalConfigProperties() {
        return configManager.toGlobalConfigProperties();
    }
}
