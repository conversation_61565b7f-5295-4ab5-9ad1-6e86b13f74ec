package com.ydwl.LiveTranscoding.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * 转码配置管理器
 * 提供统一的转码配置访问接口和配置验证功能
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TranscodingConfigManager {

    private final UnifiedTranscodingConfig unifiedTranscodingConfig;

    /**
     * 配置缓存
     */
    private final Map<String, Object> configCache = new HashMap<>();

    @PostConstruct
    public void init() {
        log.info("转码配置管理器初始化开始");
        validateConfigurations();
        cacheConfigurations();
        log.info("转码配置管理器初始化完成");
    }

    @EventListener(ApplicationReadyEvent.class)
    public void onApplicationReady() {
        printConfigurationSummary();
    }

    /**
     * 验证配置
     */
    private void validateConfigurations() {
        log.info("开始验证转码配置...");

        // 验证OSS配置
        validateOssConfig();

        // 验证阿里云配置
        validateAliyunConfig();

        // 验证转码配置
        validateTranscodeConfig();

        log.info("转码配置验证完成");
    }

    /**
     * 缓存配置
     */
    private void cacheConfigurations() {
        configCache.put("oss", unifiedTranscodingConfig.getOss());
        configCache.put("aliyun", unifiedTranscodingConfig.getAliyun());
        configCache.put("transcode", unifiedTranscodingConfig.getTranscode());
        configCache.put("liveStream", unifiedTranscodingConfig.getLiveStream());
    }

    /**
     * 获取OSS配置
     */
    public UnifiedTranscodingConfig.OssConfig getOssConfig() {
        return (UnifiedTranscodingConfig.OssConfig) configCache.get("oss");
    }

    /**
     * 获取阿里云配置
     */
    public UnifiedTranscodingConfig.AliyunConfig getAliyunConfig() {
        return (UnifiedTranscodingConfig.AliyunConfig) configCache.get("aliyun");
    }

    /**
     * 获取转码配置
     */
    public UnifiedTranscodingConfig.TranscodeConfig getTranscodeConfig() {
        return (UnifiedTranscodingConfig.TranscodeConfig) configCache.get("transcode");
    }

    /**
     * 获取直播流配置
     */
    public UnifiedTranscodingConfig.LiveStreamConfig getLiveStreamConfig() {
        return (UnifiedTranscodingConfig.LiveStreamConfig) configCache.get("liveStream");
    }

    /**
     * 获取FC配置
     */
    public UnifiedTranscodingConfig.FcConfig getFcConfig() {
        return getTranscodeConfig().getFc();
    }

    /**
     * 获取MTS配置
     */
    public UnifiedTranscodingConfig.MtsConfig getMtsConfig() {
        return getTranscodeConfig().getMts();
    }

    /**
     * 验证OSS配置
     */
    private void validateOssConfig() {
        UnifiedTranscodingConfig.OssConfig config = unifiedTranscodingConfig.getOss();
        
        if (config.getAccessKeyId() == null || config.getAccessKeyId().trim().isEmpty()) {
            log.warn("OSS AccessKeyId为空，OSS功能可能无法正常使用");
        }
        
        if (config.getAccessKeySecret() == null || config.getAccessKeySecret().trim().isEmpty()) {
            log.warn("OSS AccessKeySecret为空，OSS功能可能无法正常使用");
        }
        
        if (config.getEndpoint() == null || config.getEndpoint().trim().isEmpty()) {
            log.warn("OSS Endpoint为空，OSS功能可能无法正常使用");
        }
        
        if (config.getBucketName() == null || config.getBucketName().trim().isEmpty()) {
            log.warn("OSS BucketName为空，OSS功能可能无法正常使用");
        }
        
        log.info("OSS配置验证完成");
    }

    /**
     * 验证阿里云配置
     */
    private void validateAliyunConfig() {
        UnifiedTranscodingConfig.AliyunConfig config = unifiedTranscodingConfig.getAliyun();
        
        if (config.getAccessKeyId() == null || config.getAccessKeyId().trim().isEmpty()) {
            log.warn("阿里云AccessKeyId为空，阿里云服务可能无法正常使用");
        }
        
        if (config.getAccessKeySecret() == null || config.getAccessKeySecret().trim().isEmpty()) {
            log.warn("阿里云AccessKeySecret为空，阿里云服务可能无法正常使用");
        }
        
        log.info("阿里云配置验证完成");
    }

    /**
     * 验证转码配置
     */
    private void validateTranscodeConfig() {
        UnifiedTranscodingConfig.TranscodeConfig config = unifiedTranscodingConfig.getTranscode();
        
        // 验证FC配置
        UnifiedTranscodingConfig.FcConfig fcConfig = config.getFc();
        if (fcConfig.getFunctionName() == null || fcConfig.getFunctionName().trim().isEmpty()) {
            log.warn("FC函数名称为空，FC转码功能可能无法正常使用");
        }
        
        // 验证MTS配置
        UnifiedTranscodingConfig.MtsConfig mtsConfig = config.getMts();
        if (mtsConfig.getPipeline() == null || mtsConfig.getPipeline().trim().isEmpty()) {
            log.warn("MTS管道ID为空，MTS转码功能可能无法正常使用");
        }
        
        // 验证文件大小阈值
        UnifiedTranscodingConfig.FileThresholdConfig thresholdConfig = config.getFileThreshold();
        if (thresholdConfig.getLarge() <= 0 || thresholdConfig.getMedium() <= 0) {
            throw new IllegalArgumentException("文件大小阈值必须大于0");
        }
        
        if (thresholdConfig.getMedium() >= thresholdConfig.getLarge()) {
            throw new IllegalArgumentException("中等文件阈值必须小于大文件阈值");
        }
        
        log.info("转码配置验证完成");
    }

    /**
     * 打印配置摘要
     */
    private void printConfigurationSummary() {
        log.info("=== 转码服务配置摘要 ===");
        log.info("OSS Endpoint: {}", unifiedTranscodingConfig.getOss().getEndpoint());
        log.info("OSS Bucket: {}", unifiedTranscodingConfig.getOss().getBucketName());
        log.info("FC函数名称: {}", unifiedTranscodingConfig.getTranscode().getFc().getFunctionName());
        log.info("MTS管道ID: {}", unifiedTranscodingConfig.getTranscode().getMts().getPipeline());
        log.info("大文件阈值: {} MB", unifiedTranscodingConfig.getTranscode().getFileThreshold().getLarge() / 1024 / 1024);
        log.info("中等文件阈值: {} MB", unifiedTranscodingConfig.getTranscode().getFileThreshold().getMedium() / 1024 / 1024);
        log.info("默认回调URL: {}", unifiedTranscodingConfig.getTranscode().getCallback().getDefaultUrl());
        log.info("========================");
    }

    /**
     * 检查配置是否已更改
     */
    public boolean isConfigChanged(String configKey, Object newValue) {
        Object cachedValue = configCache.get(configKey);
        return cachedValue == null || !cachedValue.equals(newValue);
    }

    /**
     * 更新配置缓存
     */
    public void updateConfigCache(String configKey, Object newValue) {
        configCache.put(configKey, newValue);
        log.info("转码配置缓存已更新: {}", configKey);
    }

    /**
     * 获取所有配置
     */
    public Map<String, Object> getAllConfigs() {
        return new HashMap<>(configCache);
    }

    /**
     * 为兼容性提供GlobalConfigProperties格式的配置
     */
    public GlobalConfigProperties toGlobalConfigProperties() {
        GlobalConfigProperties properties = new GlobalConfigProperties();
        
        // 设置OSS配置
        GlobalConfigProperties.Oss oss = new GlobalConfigProperties.Oss();
        UnifiedTranscodingConfig.OssConfig ossConfig = getOssConfig();
        oss.setEndpoint(ossConfig.getEndpoint());
        oss.setAccessKeyId(ossConfig.getAccessKeyId());
        oss.setAccessKeySecret(ossConfig.getAccessKeySecret());
        oss.setBucketName(ossConfig.getBucketName());
        oss.setCdnDomain(ossConfig.getCdnDomain());
        oss.setCallbackUrl(ossConfig.getCallbackUrl());
        oss.setMaxFileSize(ossConfig.getMaxFileSize());
        properties.setOss(oss);
        
        // 设置Live配置
        GlobalConfigProperties.Live live = new GlobalConfigProperties.Live();
        UnifiedTranscodingConfig.AliyunConfig aliyunConfig = getAliyunConfig();
        live.setAccessKeyId(aliyunConfig.getAccessKeyId());
        live.setAccessKeySecret(aliyunConfig.getAccessKeySecret());
        live.setRegionId(aliyunConfig.getRegionId());
        live.setDomainName(aliyunConfig.getDomainName());
        live.setOssEndpoint(aliyunConfig.getOssEndpoint());
        live.setOssBucket(aliyunConfig.getOssBucket());
        properties.setLive(live);
        
        // 设置Transcode配置
        GlobalConfigProperties.Transcode transcode = new GlobalConfigProperties.Transcode();
        
        // FC配置
        GlobalConfigProperties.Fc fc = new GlobalConfigProperties.Fc();
        UnifiedTranscodingConfig.FcConfig fcConfig = getFcConfig();
        fc.setAccessKeyId(fcConfig.getAccessKeyId());
        fc.setAccessKeySecret(fcConfig.getAccessKeySecret());
        fc.setOssEndpoint(fcConfig.getOssEndpoint());
        fc.setOssBucketName(fcConfig.getOssBucketName());
        fc.setFcEndpoint(fcConfig.getFcEndpoint());
        fc.setTranscodeCompleteCallback(fcConfig.getTranscodeCompleteCallback());
        fc.setPipeline(fcConfig.getPipeline());
        fc.setFunctionName(fcConfig.getFunctionName());
        fc.setUhd480P(fcConfig.getUhd480P());
        fc.setSd720P(fcConfig.getSd720P());
        fc.setHd1080P(fcConfig.getHd1080P());
        transcode.setFc(fc);
        
        // MTS配置
        GlobalConfigProperties.Mts mts = new GlobalConfigProperties.Mts();
        UnifiedTranscodingConfig.MtsConfig mtsConfig = getMtsConfig();
        mts.setAccessKeyId(mtsConfig.getAccessKeyId());
        mts.setAccessKeySecret(mtsConfig.getAccessKeySecret());
        mts.setOssEndpoint(mtsConfig.getOssEndpoint());
        mts.setOssBucketName(mtsConfig.getOssBucketName());
        mts.setMtsEndpoint(mtsConfig.getMtsEndpoint());
        mts.setPipeline(mtsConfig.getPipeline());
        mts.setTranscodeCompleteCallback(mtsConfig.getTranscodeCompleteCallback());
        mts.setUhd480P(mtsConfig.getUhd480P());
        mts.setSd720P(mtsConfig.getSd720P());
        mts.setHd1080P(mtsConfig.getHd1080P());
        mts.setHd2k(mtsConfig.getHd2k());
        mts.setLocation(mtsConfig.getLocation());
        mts.setLarge(mtsConfig.getLarge());
        mts.setMedium(mtsConfig.getMedium());
        transcode.setMts(mts);
        
        properties.setTranscode(transcode);
        
        // 设置LiveStream配置
        GlobalConfigProperties.LiveStream liveStream = new GlobalConfigProperties.LiveStream();
        UnifiedTranscodingConfig.LiveStreamConfig liveStreamConfig = getLiveStreamConfig();
        liveStream.setPushDomain(liveStreamConfig.getPushDomain());
        liveStream.setPlayDomain(liveStreamConfig.getPlayDomain());
        liveStream.setAppName(liveStreamConfig.getAppName());
        liveStream.setPushSecretKey(liveStreamConfig.getPushSecretKey());
        liveStream.setPlaySecretKey(liveStreamConfig.getPlaySecretKey());
        liveStream.setPlayUrlExpireSeconds(liveStreamConfig.getPlayUrlExpireSeconds());
        liveStream.setLiveStartCallback(liveStreamConfig.getLiveStartCallback());
        liveStream.setLiveStopCallback(liveStreamConfig.getLiveStopCallback());
        liveStream.setLiveRecordCallback(liveStreamConfig.getLiveRecordCallback());
        liveStream.setLiveRecordCompleteCallback(liveStreamConfig.getLiveRecordCompleteCallback());
        properties.setStream(liveStream);
        
        return properties;
    }
}
