package com.ydwl.LiveTranscoding.service.strategy;

import com.ydwl.common.core.domain.dto.transcode.TranscodeInitResponseVo;
import com.ydwl.common.core.domain.dto.transcode.TranscodeRequestVo;
import com.ydwl.common.core.domain.dto.transcode.TranscodeTemplateInfo;
import com.ydwl.LiveTranscoding.service.MctTranscodeService;
import com.ydwl.LiveTranscoding.config.TranscodingConfigManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * MTS转码策略实现
 * 适合大文件和高质量转码
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class MtsTranscodeStrategy implements ITranscodeStrategy {

    private final MctTranscodeService mctTranscodeService;
    private final TranscodingConfigManager configManager;

    @Override
    public String getStrategyName() {
        return "MTS";
    }

    @Override
    public boolean supports(TranscodeRequestVo request) {
        // MTS适合大文件和高质量转码
        if (request.getFileSize() == null) {
            return false; // 未知大小，不使用MTS
        }

        long fileSizeBytes = request.getFileSize();
        long fileSizeMB = fileSizeBytes / 1024 / 1024;

        // 大于500MB的文件适合MTS
        if (fileSizeMB >= 500) {
            return true;
        }

        // 小文件但需要高质量转码也可以使用MTS
        if (isHighQualityRequired(request)) {
            return true;
        }

        return false;
    }

    /**
     * 判断是否需要高质量转码
     */
    private boolean isHighQualityRequired(TranscodeRequestVo request) {
        return request.getTemplates().stream()
                .anyMatch(template -> template.getResolution().contains("1080p") ||
                                    template.getResolution().contains("2k") ||
                                    template.getResolution().contains("4k"));
    }

    @Override
    public TranscodeInitResponseVo execute(TranscodeRequestVo request) {
        try {
            log.info("使用MTS策略执行转码，BizId: {}", request.getBizId());

            List<String> templateIds = convertToMtsTemplateIds(request.getTemplates());

            return mctTranscodeService.transcodeWithTemplates(
                request.getBucket(),
                request.getObject(),
                request.getBizId(),
                request.getCallbackUrl(),
                templateIds,
                request.getSegmentTime()
            );
        } catch (Exception e) {
            log.error("MTS转码执行失败，BizId: {}", request.getBizId(), e);
            throw new RuntimeException("MTS转码失败: " + e.getMessage());
        }
    }

    @Override
    public int getPriority() {
        return 20; // 中等优先级
    }

    @Override
    public long estimateTime(TranscodeRequestVo request) {
        // MTS转码速度较慢但质量高
        long baseTime = request.getFileSize() != null ? request.getFileSize() / 1024 / 1024 * 3 : 600;
        
        // 高分辨率需要更多时间
        if (isHighQualityRequired(request)) {
            baseTime *= 2;
        }
        
        return baseTime;
    }

    @Override
    public long estimateCost(TranscodeRequestVo request) {
        // MTS按时长计费，成本较高
        long baseCost = 15;
        
        // 高分辨率成本更高
        if (isHighQualityRequired(request)) {
            baseCost *= 2;
        }
        
        return baseCost;
    }

    private List<String> convertToMtsTemplateIds(List<TranscodeTemplateInfo> templates) {
        // 转换模板信息为MTS模板ID
        return templates.stream()
                .map(this::mapMtsTemplateId)
                .collect(Collectors.toList());
    }

    private String mapMtsTemplateId(TranscodeTemplateInfo template) {
        String resolution = template.getResolution().toLowerCase();
        
        // 使用配置管理器获取模板ID
        switch (resolution) {
            case "480p":
                return configManager.getMtsConfig().getUhd480P();
            case "720p":
                return configManager.getMtsConfig().getSd720P();
            case "1080p":
                return configManager.getMtsConfig().getHd1080P();
            case "2k":
                return configManager.getMtsConfig().getHd2k();
            default:
                // 默认使用720p模板
                return configManager.getMtsConfig().getSd720P();
        }
    }
}
