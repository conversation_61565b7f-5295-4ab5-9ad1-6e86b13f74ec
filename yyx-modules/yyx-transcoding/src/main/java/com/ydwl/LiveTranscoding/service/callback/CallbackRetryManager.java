package com.ydwl.LiveTranscoding.service.callback;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 回调重试管理器
 * 处理转码回调的重试逻辑，确保回调的可靠性
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CallbackRetryManager {

    private final RestTemplate restTemplate;
    private final CallbackRecordService callbackRecordService;

    // 回调状态跟踪
    private final Map<String, CallbackStatus> callbackStatusMap = new ConcurrentHashMap<>();

    /**
     * 发送回调（带重试机制）
     */
    @Async
    @Retryable(
        value = {Exception.class},
        maxAttempts = 5,
        backoff = @Backoff(delay = 1000, multiplier = 2, maxDelay = 30000)
    )
    public CompletableFuture<Boolean> sendCallbackWithRetry(String callbackUrl, Map<String, Object> callbackData, String bizId) {
        String callbackId = generateCallbackId(bizId);
        
        try {
            log.info("发送转码回调，CallbackId: {}, BizId: {}, URL: {}", callbackId, bizId, callbackUrl);

            // 记录回调开始
            recordCallbackStart(callbackId, bizId, callbackUrl, callbackData);

            // 发送HTTP请求
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("User-Agent", "YYX-Transcoding-Service/1.0");
            headers.set("X-Callback-Id", callbackId);
            headers.set("X-Biz-Id", bizId);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(callbackData, headers);
            ResponseEntity<String> response = restTemplate.postForEntity(callbackUrl, request, String.class);

            // 检查响应状态
            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("转码回调发送成功，CallbackId: {}, BizId: {}, 响应: {}", 
                        callbackId, bizId, response.getBody());
                
                recordCallbackSuccess(callbackId, response.getBody());
                updateCallbackStatus(callbackId, CallbackStatus.Status.SUCCESS, "回调成功");
                return CompletableFuture.completedFuture(true);
            } else {
                String errorMsg = String.format("回调响应状态异常: %d, 响应体: %s", 
                        response.getStatusCodeValue(), response.getBody());
                log.warn("转码回调响应异常，CallbackId: {}, BizId: {}, {}", callbackId, bizId, errorMsg);
                
                recordCallbackFailure(callbackId, errorMsg);
                updateCallbackStatus(callbackId, CallbackStatus.Status.FAILED, errorMsg);
                throw new CallbackException(errorMsg);
            }

        } catch (Exception e) {
            log.error("转码回调发送失败，CallbackId: {}, BizId: {}", callbackId, bizId, e);
            
            recordCallbackFailure(callbackId, e.getMessage());
            updateCallbackStatus(callbackId, CallbackStatus.Status.FAILED, e.getMessage());
            
            throw new CallbackException("回调发送失败: " + e.getMessage(), e);
        }
    }

    /**
     * 发送简单回调（无重试）
     */
    @Async
    public CompletableFuture<Boolean> sendSimpleCallback(String callbackUrl, Map<String, Object> callbackData, String bizId) {
        try {
            log.info("发送简单回调，BizId: {}, URL: {}", bizId, callbackUrl);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("User-Agent", "YYX-Transcoding-Service/1.0");
            headers.set("X-Biz-Id", bizId);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(callbackData, headers);
            ResponseEntity<String> response = restTemplate.postForEntity(callbackUrl, request, String.class);

            boolean success = response.getStatusCode().is2xxSuccessful();
            log.info("简单回调发送完成，BizId: {}, 成功: {}", bizId, success);
            
            return CompletableFuture.completedFuture(success);

        } catch (Exception e) {
            log.error("简单回调发送失败，BizId: {}", bizId, e);
            return CompletableFuture.completedFuture(false);
        }
    }

    /**
     * 批量发送回调
     */
    @Async
    public CompletableFuture<Void> sendBatchCallbacks(Map<String, CallbackRequest> callbacks) {
        log.info("开始批量发送回调，数量: {}", callbacks.size());

        CompletableFuture<?>[] futures = callbacks.entrySet().stream()
                .map(entry -> {
                    CallbackRequest request = entry.getValue();
                    return sendCallbackWithRetry(request.getUrl(), request.getData(), request.getBizId());
                })
                .toArray(CompletableFuture[]::new);

        return CompletableFuture.allOf(futures)
                .thenRun(() -> log.info("批量回调发送完成"));
    }

    /**
     * 获取回调状态
     */
    public CallbackStatus getCallbackStatus(String callbackId) {
        return callbackStatusMap.get(callbackId);
    }

    /**
     * 获取业务ID的所有回调状态
     */
    public Map<String, CallbackStatus> getCallbackStatusByBizId(String bizId) {
        return callbackStatusMap.entrySet().stream()
                .filter(entry -> entry.getValue().getBizId().equals(bizId))
                .collect(java.util.stream.Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue
                ));
    }

    /**
     * 记录回调开始
     */
    private void recordCallbackStart(String callbackId, String bizId, String url, Map<String, Object> data) {
        CallbackStatus status = new CallbackStatus();
        status.setCallbackId(callbackId);
        status.setBizId(bizId);
        status.setUrl(url);
        status.setStatus(CallbackStatus.Status.PENDING);
        status.setStartTime(LocalDateTime.now());
        status.setAttemptCount(1);

        callbackStatusMap.put(callbackId, status);
        callbackRecordService.recordCallbackStart(callbackId, bizId, url, data);
    }

    /**
     * 记录回调成功
     */
    private void recordCallbackSuccess(String callbackId, String response) {
        callbackRecordService.recordCallbackSuccess(callbackId, response);
    }

    /**
     * 记录回调失败
     */
    private void recordCallbackFailure(String callbackId, String errorMessage) {
        CallbackStatus status = callbackStatusMap.get(callbackId);
        if (status != null) {
            status.incrementAttemptCount();
        }
        callbackRecordService.recordCallbackFailure(callbackId, errorMessage);
    }

    /**
     * 更新回调状态
     */
    private void updateCallbackStatus(String callbackId, CallbackStatus.Status status, String message) {
        CallbackStatus callbackStatus = callbackStatusMap.get(callbackId);
        if (callbackStatus != null) {
            callbackStatus.setStatus(status);
            callbackStatus.setMessage(message);
            callbackStatus.setEndTime(LocalDateTime.now());
        }
    }

    /**
     * 生成回调ID
     */
    private String generateCallbackId(String bizId) {
        return "CB_" + bizId + "_" + System.currentTimeMillis();
    }

    /**
     * 回调状态类
     */
    public static class CallbackStatus {
        public enum Status {
            PENDING,    // 等待中
            SUCCESS,    // 成功
            FAILED,     // 失败
            TIMEOUT     // 超时
        }

        private String callbackId;
        private String bizId;
        private String url;
        private Status status;
        private String message;
        private LocalDateTime startTime;
        private LocalDateTime endTime;
        private int attemptCount;

        // Getters and Setters
        public String getCallbackId() { return callbackId; }
        public void setCallbackId(String callbackId) { this.callbackId = callbackId; }
        public String getBizId() { return bizId; }
        public void setBizId(String bizId) { this.bizId = bizId; }
        public String getUrl() { return url; }
        public void setUrl(String url) { this.url = url; }
        public Status getStatus() { return status; }
        public void setStatus(Status status) { this.status = status; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public LocalDateTime getStartTime() { return startTime; }
        public void setStartTime(LocalDateTime startTime) { this.startTime = startTime; }
        public LocalDateTime getEndTime() { return endTime; }
        public void setEndTime(LocalDateTime endTime) { this.endTime = endTime; }
        public int getAttemptCount() { return attemptCount; }
        public void setAttemptCount(int attemptCount) { this.attemptCount = attemptCount; }
        public void incrementAttemptCount() { this.attemptCount++; }
    }

    /**
     * 回调请求类
     */
    public static class CallbackRequest {
        private String url;
        private Map<String, Object> data;
        private String bizId;

        public CallbackRequest(String url, Map<String, Object> data, String bizId) {
            this.url = url;
            this.data = data;
            this.bizId = bizId;
        }

        // Getters
        public String getUrl() { return url; }
        public Map<String, Object> getData() { return data; }
        public String getBizId() { return bizId; }
    }

    /**
     * 回调异常类
     */
    public static class CallbackException extends RuntimeException {
        public CallbackException(String message) {
            super(message);
        }

        public CallbackException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
