package com.ydwl.LiveTranscoding.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 统一的转码配置类
 * 整合所有转码相关配置，避免配置分散
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "ydwl.transcoding")
public class UnifiedTranscodingConfig {

    /**
     * OSS配置
     */
    private OssConfig oss = new OssConfig();

    /**
     * 阿里云配置
     */
    private AliyunConfig aliyun = new AliyunConfig();

    /**
     * 转码配置
     */
    private TranscodeConfig transcode = new TranscodeConfig();

    /**
     * 直播流配置
     */
    private LiveStreamConfig liveStream = new LiveStreamConfig();

    /**
     * OSS配置
     */
    @Data
    public static class OssConfig {
        /**
         * OSS地域节点
         */
        private String endpoint;

        /**
         * 访问密钥ID
         */
        private String accessKeyId;

        /**
         * 访问密钥密码
         */
        private String accessKeySecret;

        /**
         * 存储桶名称
         */
        private String bucketName;

        /**
         * CDN域名
         */
        private String cdnDomain;

        /**
         * 回调域名
         */
        private String callbackDomain;

        /**
         * 回调URL
         */
        private String callbackUrl;

        /**
         * 最大文件大小
         */
        private Long maxFileSize = 2147483648L; // 2GB
    }

    /**
     * 阿里云配置
     */
    @Data
    public static class AliyunConfig {
        /**
         * 阿里云AccessKeyId
         */
        private String accessKeyId;

        /**
         * 阿里云AccessKeySecret
         */
        private String accessKeySecret;

        /**
         * 地域ID
         */
        private String regionId = "cn-beijing";

        /**
         * 直播推流域名
         */
        private String domainName;

        /**
         * OSS Endpoint
         */
        private String ossEndpoint;

        /**
         * OSS Bucket名称
         */
        private String ossBucket;
    }

    /**
     * 转码配置
     */
    @Data
    public static class TranscodeConfig {
        /**
         * FC转码配置
         */
        private FcConfig fc = new FcConfig();

        /**
         * MTS转码配置
         */
        private MtsConfig mts = new MtsConfig();

        /**
         * 文件大小阈值配置
         */
        private FileThresholdConfig fileThreshold = new FileThresholdConfig();

        /**
         * 回调配置
         */
        private CallbackConfig callback = new CallbackConfig();
    }

    /**
     * FC转码配置
     */
    @Data
    public static class FcConfig {
        /**
         * 阿里云AccessKeyId
         */
        private String accessKeyId;

        /**
         * 阿里云AccessKeySecret
         */
        private String accessKeySecret;

        /**
         * OSS Endpoint
         */
        private String ossEndpoint;

        /**
         * OSS Bucket名称
         */
        private String ossBucketName;

        /**
         * FC Endpoint
         */
        private String fcEndpoint;

        /**
         * 转码完成回调URL
         */
        private String transcodeCompleteCallback;

        /**
         * 管道ID
         */
        private String pipeline = "default-pipeline";

        /**
         * 函数名称
         */
        private String functionName = "video-turncode-ydwl-prod";

        /**
         * 480P模板
         */
        private String uhd480P = "FC-480p-template";

        /**
         * 720P模板
         */
        private String sd720P = "FC-720p-template";

        /**
         * 1080P模板
         */
        private String hd1080P = "FC-1080p-template";
    }

    /**
     * MTS转码配置
     */
    @Data
    public static class MtsConfig {
        /**
         * 阿里云AccessKeyId
         */
        private String accessKeyId;

        /**
         * 阿里云AccessKeySecret
         */
        private String accessKeySecret;

        /**
         * OSS Endpoint
         */
        private String ossEndpoint;

        /**
         * OSS Bucket名称
         */
        private String ossBucketName;

        /**
         * MTS Endpoint
         */
        private String mtsEndpoint;

        /**
         * 管道ID
         */
        private String pipeline;

        /**
         * 转码完成回调URL
         */
        private String transcodeCompleteCallback;

        /**
         * 480P模板
         */
        private String uhd480P;

        /**
         * 720P模板
         */
        private String sd720P;

        /**
         * 1080P模板
         */
        private String hd1080P;

        /**
         * 2K模板
         */
        private String hd2k;

        /**
         * 地域
         */
        private String location = "oss-cn-beijing";

        /**
         * 大文件阈值（字节）
         */
        private Long large = 524288000L; // 500MB

        /**
         * 中等文件阈值（字节）
         */
        private Long medium = 104857600L; // 100MB
    }

    /**
     * 文件大小阈值配置
     */
    @Data
    public static class FileThresholdConfig {
        /**
         * 大文件阈值（字节）
         */
        private Long large = 524288000L; // 500MB

        /**
         * 中等文件阈值（字节）
         */
        private Long medium = 104857600L; // 100MB
    }

    /**
     * 回调配置
     */
    @Data
    public static class CallbackConfig {
        /**
         * 默认回调URL
         */
        private String defaultUrl = "https://yd-live.vip.cpolar.cn/live/transcode/callback";

        /**
         * FC回调URL
         */
        private String fcUrl;

        /**
         * MTS回调URL
         */
        private String mtsUrl;

        /**
         * 获取FC回调URL，如果未设置则使用默认URL
         */
        public String getFcUrl() {
            return fcUrl != null ? fcUrl : defaultUrl;
        }

        /**
         * 获取MTS回调URL，如果未设置则使用默认URL
         */
        public String getMtsUrl() {
            return mtsUrl != null ? mtsUrl : defaultUrl;
        }
    }

    /**
     * 直播流配置
     */
    @Data
    public static class LiveStreamConfig {
        /**
         * 推流域名
         */
        private String pushDomain;

        /**
         * 播放域名
         */
        private String playDomain;

        /**
         * 应用名称
         */
        private String appName;

        /**
         * 推流鉴权密钥
         */
        private String pushSecretKey;

        /**
         * 播放鉴权密钥
         */
        private String playSecretKey;

        /**
         * 播放URL有效期（秒）
         */
        private Long playUrlExpireSeconds = 86400L;

        /**
         * 直播开始回调
         */
        private String liveStartCallback;

        /**
         * 直播结束回调
         */
        private String liveStopCallback;

        /**
         * 直播录制回调
         */
        private String liveRecordCallback;

        /**
         * 直播录制完成回调
         */
        private String liveRecordCompleteCallback;
    }
}
