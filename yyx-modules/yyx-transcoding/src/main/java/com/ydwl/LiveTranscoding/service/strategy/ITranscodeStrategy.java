package com.ydwl.LiveTranscoding.service.strategy;

import com.ydwl.common.core.domain.dto.transcode.TranscodeInitResponseVo;
import com.ydwl.common.core.domain.dto.transcode.TranscodeRequestVo;

/**
 * 转码策略接口
 * 定义不同转码方式的统一接口
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
public interface ITranscodeStrategy {

    /**
     * 获取策略名称
     *
     * @return 策略名称
     */
    String getStrategyName();

    /**
     * 判断是否支持该请求
     *
     * @param request 转码请求
     * @return 是否支持
     */
    boolean supports(TranscodeRequestVo request);

    /**
     * 执行转码
     *
     * @param request 转码请求
     * @return 转码响应
     */
    TranscodeInitResponseVo execute(TranscodeRequestVo request);

    /**
     * 获取策略优先级（数字越小优先级越高）
     *
     * @return 优先级
     */
    default int getPriority() {
        return 100;
    }

    /**
     * 预估转码时间（秒）
     *
     * @param request 转码请求
     * @return 预估时间
     */
    default long estimateTime(TranscodeRequestVo request) {
        // 默认预估：文件大小(MB) * 2 秒
        return request.getFileSize() != null ? request.getFileSize() / 1024 / 1024 * 2 : 300;
    }

    /**
     * 预估转码成本
     *
     * @param request 转码请求
     * @return 预估成本（分）
     */
    default long estimateCost(TranscodeRequestVo request) {
        // 默认预估成本
        return 10;
    }
}
