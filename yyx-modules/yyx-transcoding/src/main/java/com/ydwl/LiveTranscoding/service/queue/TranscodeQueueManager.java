package com.ydwl.LiveTranscoding.service.queue;

import com.ydwl.common.core.domain.dto.transcode.TranscodeRequestVo;
import com.ydwl.LiveTranscoding.config.TranscodingConfigManager;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 转码队列管理器
 * 管理转码任务的排队、调度和执行
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TranscodeQueueManager {

    private final TranscodingConfigManager configManager;
    private final TranscodeExecutor transcodeExecutor;

    // 不同优先级的队列
    private final PriorityBlockingQueue<TranscodeTask> highPriorityQueue = new PriorityBlockingQueue<>();
    private final PriorityBlockingQueue<TranscodeTask> normalPriorityQueue = new PriorityBlockingQueue<>();
    private final PriorityBlockingQueue<TranscodeTask> lowPriorityQueue = new PriorityBlockingQueue<>();

    // 正在执行的任务
    private final Map<String, TranscodeTask> runningTasks = new ConcurrentHashMap<>();

    // 任务统计
    private final AtomicInteger totalSubmitted = new AtomicInteger(0);
    private final AtomicInteger totalCompleted = new AtomicInteger(0);
    private final AtomicInteger totalFailed = new AtomicInteger(0);

    // 线程池
    private ExecutorService executorService;

    @PostConstruct
    public void init() {
        // 根据系统CPU核心数确定线程池大小
        int cpuCores = Runtime.getRuntime().availableProcessors();
        int threadPoolSize = Math.max(cpuCores, 5); // 最少5个线程
        threadPoolSize = Math.min(threadPoolSize, 20); // 最多20个线程

        this.executorService = new ThreadPoolExecutor(
            threadPoolSize / 2,  // 核心线程数
            threadPoolSize,      // 最大线程数
            60L,                 // 空闲时间
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(100), // 队列大小
            new ThreadFactory() {
                private final AtomicInteger counter = new AtomicInteger(0);
                @Override
                public Thread newThread(Runnable r) {
                    Thread thread = new Thread(r, "transcode-worker-" + counter.incrementAndGet());
                    thread.setDaemon(true);
                    return thread;
                }
            },
            new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略
        );

        log.info("转码队列管理器初始化完成，线程池大小: {}", threadPoolSize);
    }

    /**
     * 提交转码任务
     */
    public String submitTask(TranscodeRequestVo request, TaskPriority priority) {
        String taskId = generateTaskId();
        TranscodeTask task = new TranscodeTask(taskId, request, priority);

        // 根据优先级加入不同队列
        switch (priority) {
            case HIGH:
                highPriorityQueue.offer(task);
                break;
            case NORMAL:
                normalPriorityQueue.offer(task);
                break;
            case LOW:
                lowPriorityQueue.offer(task);
                break;
        }

        totalSubmitted.incrementAndGet();
        log.info("转码任务已提交，TaskId: {}, BizId: {}, 优先级: {}, 队列大小: {}",
                taskId, request.getBizId(), priority, getQueueSize());

        return taskId;
    }

    /**
     * 定时调度任务
     */
    @Scheduled(fixedDelay = 1000) // 每秒检查一次
    public void scheduleTasks() {
        try {
            // 检查是否有可用的执行槽位
            int maxConcurrent = getMaxConcurrentTasks();
            int currentRunning = runningTasks.size();

            if (currentRunning >= maxConcurrent) {
                return; // 已达到最大并发数
            }

            // 按优先级顺序获取任务
            TranscodeTask task = getNextTask();
            if (task != null) {
                executeTaskAsync(task);
            }

        } catch (Exception e) {
            log.error("调度转码任务失败", e);
        }
    }

    /**
     * 异步执行转码任务
     */
    @Async
    public CompletableFuture<Void> executeTaskAsync(TranscodeTask task) {
        String taskId = task.getTaskId();

        try {
            // 标记任务为运行中
            runningTasks.put(taskId, task);
            task.setStatus(TranscodeQueueManager.TaskStatus.RUNNING);
            task.setStartTime(System.currentTimeMillis());

            log.info("开始执行转码任务，TaskId: {}, BizId: {}", taskId, task.getRequest().getBizId());

            // 执行转码
            transcodeExecutor.execute(task);

            // 任务完成
            task.setStatus(TranscodeQueueManager.TaskStatus.COMPLETED);
            task.setEndTime(System.currentTimeMillis());
            totalCompleted.incrementAndGet();

            log.info("转码任务执行完成，TaskId: {}, BizId: {}, 耗时: {}ms",
                    taskId, task.getRequest().getBizId(), task.getExecutionTime());

        } catch (Exception e) {
            // 任务失败
            task.setStatus(TranscodeQueueManager.TaskStatus.FAILED);
            task.setEndTime(System.currentTimeMillis());
            task.setErrorMessage(e.getMessage());
            totalFailed.incrementAndGet();

            log.error("转码任务执行失败，TaskId: {}, BizId: {}", taskId, task.getRequest().getBizId(), e);

        } finally {
            // 从运行中任务列表移除
            runningTasks.remove(taskId);
        }

        return CompletableFuture.completedFuture(null);
    }

    /**
     * 获取下一个要执行的任务（按优先级）
     */
    private TranscodeTask getNextTask() {
        // 优先处理高优先级任务
        TranscodeTask task = highPriorityQueue.poll();
        if (task != null) {
            return task;
        }

        // 然后处理普通优先级任务
        task = normalPriorityQueue.poll();
        if (task != null) {
            return task;
        }

        // 最后处理低优先级任务
        return lowPriorityQueue.poll();
    }

    /**
     * 获取最大并发任务数
     */
    private int getMaxConcurrentTasks() {
        // 根据系统资源动态调整
        int cpuCores = Runtime.getRuntime().availableProcessors();
        return Math.min(cpuCores * 2, 10); // 最多10个并发任务
    }

    /**
     * 获取队列统计信息
     */
    public QueueStatistics getQueueStatistics() {
        QueueStatistics stats = new QueueStatistics();
        stats.setHighPriorityQueueSize(highPriorityQueue.size());
        stats.setNormalPriorityQueueSize(normalPriorityQueue.size());
        stats.setLowPriorityQueueSize(lowPriorityQueue.size());
        stats.setRunningTasksCount(runningTasks.size());
        stats.setTotalSubmitted(totalSubmitted.get());
        stats.setTotalCompleted(totalCompleted.get());
        stats.setTotalFailed(totalFailed.get());
        return stats;
    }

    /**
     * 获取总队列大小
     */
    public int getQueueSize() {
        return highPriorityQueue.size() + normalPriorityQueue.size() + lowPriorityQueue.size();
    }

    /**
     * 取消任务
     */
    public boolean cancelTask(String taskId) {
        // 从队列中移除
        boolean removed = removeFromQueues(taskId);

        // 如果正在运行，尝试中断
        TranscodeTask runningTask = runningTasks.get(taskId);
        if (runningTask != null) {
            runningTask.setStatus(TranscodeQueueManager.TaskStatus.CANCELLED);
            // 这里可以添加中断逻辑
            return true;
        }

        return removed;
    }

    /**
     * 从所有队列中移除任务
     */
    private boolean removeFromQueues(String taskId) {
        return highPriorityQueue.removeIf(task -> task.getTaskId().equals(taskId)) ||
               normalPriorityQueue.removeIf(task -> task.getTaskId().equals(taskId)) ||
               lowPriorityQueue.removeIf(task -> task.getTaskId().equals(taskId));
    }

    /**
     * 生成任务ID
     */
    private String generateTaskId() {
        return "TASK_" + System.currentTimeMillis() + "_" + java.util.UUID.randomUUID().toString().substring(0, 8);
    }

    /**
     * 任务优先级
     */
    public enum TaskPriority {
        HIGH(1),    // 高优先级（实时直播转码）
        NORMAL(2),  // 普通优先级（录播转码）
        LOW(3);     // 低优先级（批量转码）

        private final int level;

        TaskPriority(int level) {
            this.level = level;
        }

        public int getLevel() {
            return level;
        }
    }

    /**
     * 任务状态
     */
    public enum TaskStatus {
        PENDING,    // 等待中
        RUNNING,    // 运行中
        COMPLETED,  // 已完成
        FAILED,     // 失败
        CANCELLED   // 已取消
    }

    /**
     * 队列统计信息
     */
    public static class QueueStatistics {
        private int highPriorityQueueSize;
        private int normalPriorityQueueSize;
        private int lowPriorityQueueSize;
        private int runningTasksCount;
        private int totalSubmitted;
        private int totalCompleted;
        private int totalFailed;

        // Getters and Setters
        public int getHighPriorityQueueSize() { return highPriorityQueueSize; }
        public void setHighPriorityQueueSize(int highPriorityQueueSize) { this.highPriorityQueueSize = highPriorityQueueSize; }
        public int getNormalPriorityQueueSize() { return normalPriorityQueueSize; }
        public void setNormalPriorityQueueSize(int normalPriorityQueueSize) { this.normalPriorityQueueSize = normalPriorityQueueSize; }
        public int getLowPriorityQueueSize() { return lowPriorityQueueSize; }
        public void setLowPriorityQueueSize(int lowPriorityQueueSize) { this.lowPriorityQueueSize = lowPriorityQueueSize; }
        public int getRunningTasksCount() { return runningTasksCount; }
        public void setRunningTasksCount(int runningTasksCount) { this.runningTasksCount = runningTasksCount; }
        public int getTotalSubmitted() { return totalSubmitted; }
        public void setTotalSubmitted(int totalSubmitted) { this.totalSubmitted = totalSubmitted; }
        public int getTotalCompleted() { return totalCompleted; }
        public void setTotalCompleted(int totalCompleted) { this.totalCompleted = totalCompleted; }
        public int getTotalFailed() { return totalFailed; }
        public void setTotalFailed(int totalFailed) { this.totalFailed = totalFailed; }
    }
}
