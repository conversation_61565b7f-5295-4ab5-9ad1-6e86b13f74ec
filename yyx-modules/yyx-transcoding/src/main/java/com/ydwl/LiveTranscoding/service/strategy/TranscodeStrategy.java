package com.ydwl.LiveTranscoding.service.strategy;

import com.ydwl.common.core.domain.dto.transcode.TranscodeInitResponseVo;
import com.ydwl.common.core.domain.dto.transcode.TranscodeRequestVo;
import com.ydwl.common.core.domain.dto.transcode.TranscodeTemplateInfo;
import com.ydwl.LiveTranscoding.service.FcTranscodeService;
import com.ydwl.LiveTranscoding.config.GlobalConfigProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 转码策略接口
 * 定义不同转码方式的统一接口
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
public interface TranscodeStrategy {

    /**
     * 获取策略名称
     *
     * @return 策略名称
     */
    String getStrategyName();

    /**
     * 判断是否支持该请求
     *
     * @param request 转码请求
     * @return 是否支持
     */
    boolean supports(TranscodeRequestVo request);

    /**
     * 执行转码
     *
     * @param request 转码请求
     * @return 转码响应
     */
    TranscodeInitResponseVo execute(TranscodeRequestVo request);

    /**
     * 获取策略优先级（数字越小优先级越高）
     *
     * @return 优先级
     */
    default int getPriority() {
        return 100;
    }

    /**
     * 预估转码时间（秒）
     *
     * @param request 转码请求
     * @return 预估时间
     */
    default long estimateTime(TranscodeRequestVo request) {
        // 默认预估：文件大小(MB) * 2 秒
        return request.getFileSize() != null ? request.getFileSize() / 1024 / 1024 * 2 : 300;
    }

    /**
     * 预估转码成本
     *
     * @param request 转码请求
     * @return 预估成本（分）
     */
    default long estimateCost(TranscodeRequestVo request) {
        // 默认预估成本
        return 10;
    }
}

/**
 * FC转码策略实现
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class FcTranscodeStrategy implements TranscodeStrategy {

    private final FcTranscodeService fcTranscodeService;
    private final GlobalConfigProperties properties;

    @Override
    public String getStrategyName() {
        return "FC";
    }

    @Override
    public boolean supports(TranscodeRequestVo request) {
        // FC适合小文件快速转码
        return request.getFileSize() == null || request.getFileSize() < 500 * 1024 * 1024; // 500MB以下
    }

    @Override
    public TranscodeInitResponseVo execute(TranscodeRequestVo request) {
        try {
            log.info("使用FC策略执行转码，BizId: {}", request.getBizId());

            List<String> templateIds = convertToFcTemplateIds(request.getTemplates());

            return fcTranscodeService.transcodeWithTemplates(
                request.getBucket(),
                request.getObject(),
                request.getBizId(),
                request.getCallbackUrl(),
                templateIds,
                request.getSegmentTime()
            );
        } catch (Exception e) {
            log.error("FC转码执行失败，BizId: {}", request.getBizId(), e);
            throw new RuntimeException("FC转码失败: " + e.getMessage());
        }
    }

    @Override
    public int getPriority() {
        return 10; // 高优先级
    }

    @Override
    public long estimateTime(TranscodeRequestVo request) {
        // FC转码速度较快
        return request.getFileSize() != null ? request.getFileSize() / 1024 / 1024 : 180;
    }

    @Override
    public long estimateCost(TranscodeRequestVo request) {
        // FC按调用次数计费，成本较低
        return 5;
    }

    private List<String> convertToFcTemplateIds(List<TranscodeTemplateInfo> templates) {
        // 转换模板信息为FC模板ID
        return templates.stream()
            .map(template -> "FC-" + template.getResolution() + "-template")
            .collect(Collectors.toList());
    }
}
