package com.ydwl.LiveTranscoding.service.strategy;

import com.ydwl.common.core.domain.dto.transcode.TranscodeInitResponseVo;
import com.ydwl.common.core.domain.dto.transcode.TranscodeRequestVo;
import com.ydwl.common.core.domain.dto.transcode.TranscodeTemplateInfo;
import com.ydwl.LiveTranscoding.service.FcTranscodeService;
import com.ydwl.LiveTranscoding.config.TranscodingConfigManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * FC转码策略实现
 * 适合小文件快速转码
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class FcTranscodeStrategyImpl implements ITranscodeStrategy {

    private final FcTranscodeService fcTranscodeService;
    private final TranscodingConfigManager configManager;

    @Override
    public String getStrategyName() {
        return "FC";
    }

    @Override
    public boolean supports(TranscodeRequestVo request) {
        // FC适合小文件快速转码，考虑多个因素
        if (request.getFileSize() == null) {
            return true; // 未知大小，优先尝试FC
        }

        long fileSizeBytes = request.getFileSize();
        long fileSizeMB = fileSizeBytes / 1024 / 1024;

        // 小于500MB的文件适合FC
        if (fileSizeMB < 500) {
            return true;
        }

        // 500MB-1GB的文件，根据格式和分辨率决定
        if (fileSizeMB < 1024) {
            // 如果是简单格式且分辨率不高，也可以用FC
            return isSimpleFormat(request) && !isHighResolution(request);
        }

        return false;
    }

    /**
     * 判断是否为简单格式
     */
    private boolean isSimpleFormat(TranscodeRequestVo request) {
        String object = request.getObject().toLowerCase();
        return object.endsWith(".mp4") || object.endsWith(".mov");
    }

    /**
     * 判断是否为高分辨率
     */
    private boolean isHighResolution(TranscodeRequestVo request) {
        return request.getTemplates().stream()
                .anyMatch(template -> template.getResolution().contains("1080p") ||
                                    template.getResolution().contains("2k") ||
                                    template.getResolution().contains("4k"));
    }

    @Override
    public TranscodeInitResponseVo execute(TranscodeRequestVo request) {
        try {
            log.info("使用FC策略执行转码，BizId: {}", request.getBizId());

            List<String> templateIds = convertToFcTemplateIds(request.getTemplates());

            return fcTranscodeService.transcodeWithTemplates(
                request.getBucket(),
                request.getObject(),
                request.getBizId(),
                request.getCallbackUrl(),
                templateIds,
                request.getSegmentTime()
            );
        } catch (Exception e) {
            log.error("FC转码执行失败，BizId: {}", request.getBizId(), e);
            throw new RuntimeException("FC转码失败: " + e.getMessage());
        }
    }

    @Override
    public int getPriority() {
        return 10; // 高优先级
    }

    @Override
    public long estimateTime(TranscodeRequestVo request) {
        // FC转码速度较快
        return request.getFileSize() != null ? request.getFileSize() / 1024 / 1024 : 180;
    }

    @Override
    public long estimateCost(TranscodeRequestVo request) {
        // FC按调用次数计费，成本较低
        return 5;
    }

    private List<String> convertToFcTemplateIds(List<TranscodeTemplateInfo> templates) {
        // 转换模板信息为FC模板ID
        return templates.stream()
                .map(this::mapFcTemplateId)
                .collect(Collectors.toList());
    }

    private String mapFcTemplateId(TranscodeTemplateInfo template) {
        String resolution = template.getResolution().toLowerCase();
        
        // 使用配置管理器获取模板ID
        switch (resolution) {
            case "480p":
                return configManager.getFcConfig().getUhd480P();
            case "720p":
                return configManager.getFcConfig().getSd720P();
            case "1080p":
                return configManager.getFcConfig().getHd1080P();
            default:
                // 默认使用720p模板
                return configManager.getFcConfig().getSd720P();
        }
    }
}
