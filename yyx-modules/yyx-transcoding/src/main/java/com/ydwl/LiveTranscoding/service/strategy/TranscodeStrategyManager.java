package com.ydwl.LiveTranscoding.service.strategy;

import com.ydwl.common.core.domain.dto.transcode.TranscodeRequestVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Optional;

/**
 * 转码策略管理器
 * 负责选择最合适的转码策略
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TranscodeStrategyManager {

    private final List<ITranscodeStrategy> strategies;

    /**
     * 选择最佳转码策略
     *
     * @param request 转码请求
     * @return 最佳策略
     */
    public ITranscodeStrategy selectBestStrategy(TranscodeRequestVo request) {
        log.debug("为转码请求选择策略，BizId: {}, FileSize: {}", 
                request.getBizId(), request.getFileSize());

        // 找到所有支持该请求的策略
        List<ITranscodeStrategy> supportedStrategies = strategies.stream()
                .filter(strategy -> strategy.supports(request))
                .sorted(Comparator.comparingInt(ITranscodeStrategy::getPriority))
                .toList();

        if (supportedStrategies.isEmpty()) {
            log.warn("没有找到支持的转码策略，BizId: {}", request.getBizId());
            throw new RuntimeException("没有可用的转码策略");
        }

        // 选择最佳策略（考虑优先级、成本、时间等因素）
        ITranscodeStrategy bestStrategy = selectOptimalStrategy(supportedStrategies, request);
        
        log.info("选择转码策略: {}, BizId: {}, 预估时间: {}s, 预估成本: {}分", 
                bestStrategy.getStrategyName(), request.getBizId(),
                bestStrategy.estimateTime(request), bestStrategy.estimateCost(request));

        return bestStrategy;
    }

    /**
     * 选择最优策略
     */
    private ITranscodeStrategy selectOptimalStrategy(List<ITranscodeStrategy> strategies, TranscodeRequestVo request) {
        if (strategies.size() == 1) {
            return strategies.get(0);
        }

        // 综合考虑时间和成本选择最优策略
        return strategies.stream()
                .min(Comparator.comparingLong(strategy -> calculateScore(strategy, request)))
                .orElse(strategies.get(0));
    }

    /**
     * 计算策略评分（越小越好）
     */
    private long calculateScore(ITranscodeStrategy strategy, TranscodeRequestVo request) {
        long timeScore = strategy.estimateTime(request);
        long costScore = strategy.estimateCost(request) * 10; // 成本权重更高
        long priorityScore = strategy.getPriority() * 100; // 优先级权重最高
        
        return priorityScore + timeScore + costScore;
    }

    /**
     * 获取指定名称的策略
     */
    public Optional<ITranscodeStrategy> getStrategy(String strategyName) {
        return strategies.stream()
                .filter(strategy -> strategy.getStrategyName().equalsIgnoreCase(strategyName))
                .findFirst();
    }

    /**
     * 获取所有可用策略
     */
    public List<ITranscodeStrategy> getAllStrategies() {
        return strategies.stream()
                .sorted(Comparator.comparingInt(ITranscodeStrategy::getPriority))
                .toList();
    }

    /**
     * 获取策略统计信息
     */
    public StrategyStatistics getStatistics() {
        StrategyStatistics stats = new StrategyStatistics();
        stats.setTotalStrategies(strategies.size());
        
        long fcCount = strategies.stream()
                .filter(s -> "FC".equals(s.getStrategyName()))
                .count();
        stats.setFcStrategies((int) fcCount);
        
        long mtsCount = strategies.stream()
                .filter(s -> "MTS".equals(s.getStrategyName()))
                .count();
        stats.setMtsStrategies((int) mtsCount);
        
        return stats;
    }

    /**
     * 策略统计信息
     */
    public static class StrategyStatistics {
        private int totalStrategies;
        private int fcStrategies;
        private int mtsStrategies;

        // Getters and Setters
        public int getTotalStrategies() { return totalStrategies; }
        public void setTotalStrategies(int totalStrategies) { this.totalStrategies = totalStrategies; }
        public int getFcStrategies() { return fcStrategies; }
        public void setFcStrategies(int fcStrategies) { this.fcStrategies = fcStrategies; }
        public int getMtsStrategies() { return mtsStrategies; }
        public void setMtsStrategies(int mtsStrategies) { this.mtsStrategies = mtsStrategies; }
    }
}
