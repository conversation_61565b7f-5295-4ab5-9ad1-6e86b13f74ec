package com.ydwl.LiveTranscoding.service.queue;

import com.ydwl.common.core.service.IUniversalTranscodeService;
import com.ydwl.common.core.domain.dto.transcode.TranscodeInitResponseVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 转码执行器
 * 负责实际执行转码任务
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TranscodeExecutor {

    private final IUniversalTranscodeService universalTranscodeService;

    /**
     * 执行转码任务
     *
     * @param task 转码任务
     * @throws Exception 执行异常
     */
    public void execute(TranscodeTask task) throws Exception {
        String taskId = task.getTaskId();
        String bizId = task.getRequest().getBizId();

        try {
            log.info("开始执行转码任务，TaskId: {}, BizId: {}", taskId, bizId);

            // 调用转码服务
            TranscodeInitResponseVo response = universalTranscodeService.transcode(task.getRequest());

            if (response != null && response.isSuccess()) {
                log.info("转码任务提交成功，TaskId: {}, BizId: {}, JobId: {}",
                        taskId, bizId, response.getJobId());
            } else {
                String errorMsg = response != null ? response.getMessage() : "转码服务返回空响应";
                throw new TranscodeExecutionException("转码任务提交失败: " + errorMsg);
            }

        } catch (Exception e) {
            log.error("转码任务执行失败，TaskId: {}, BizId: {}", taskId, bizId, e);
            throw new TranscodeExecutionException("转码任务执行失败: " + e.getMessage(), e);
        }
    }

    /**
     * 转码执行异常
     */
    public static class TranscodeExecutionException extends Exception {
        public TranscodeExecutionException(String message) {
            super(message);
        }

        public TranscodeExecutionException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
