package com.ydwl.LiveTranscoding.service.queue;

import com.ydwl.common.core.domain.dto.transcode.TranscodeRequestVo;
import lombok.Data;

/**
 * 转码任务
 * 封装转码请求和任务状态信息
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Data
public class TranscodeTask implements Comparable<TranscodeTask> {

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 转码请求
     */
    private TranscodeRequestVo request;

    /**
     * 任务优先级
     */
    private TranscodeQueueManager.TaskPriority priority;

    /**
     * 任务状态
     */
    private TranscodeQueueManager.TaskStatus status;

    /**
     * 创建时间
     */
    private long createTime;

    /**
     * 开始时间
     */
    private long startTime;

    /**
     * 结束时间
     */
    private long endTime;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 重试次数
     */
    private int retryCount;

    /**
     * 最大重试次数
     */
    private int maxRetryCount = 3;

    public TranscodeTask(String taskId, TranscodeRequestVo request, TranscodeQueueManager.TaskPriority priority) {
        this.taskId = taskId;
        this.request = request;
        this.priority = priority;
        this.status = TranscodeQueueManager.TaskStatus.PENDING;
        this.createTime = System.currentTimeMillis();
        this.retryCount = 0;
    }

    /**
     * 获取任务执行时间（毫秒）
     */
    public long getExecutionTime() {
        if (startTime > 0 && endTime > 0) {
            return endTime - startTime;
        }
        return 0;
    }

    /**
     * 获取任务等待时间（毫秒）
     */
    public long getWaitTime() {
        if (startTime > 0) {
            return startTime - createTime;
        }
        return System.currentTimeMillis() - createTime;
    }

    /**
     * 是否可以重试
     */
    public boolean canRetry() {
        return retryCount < maxRetryCount && 
               (status == TranscodeQueueManager.TaskStatus.FAILED || 
                status == TranscodeQueueManager.TaskStatus.CANCELLED);
    }

    /**
     * 增加重试次数
     */
    public void incrementRetryCount() {
        this.retryCount++;
    }

    /**
     * 重置任务状态（用于重试）
     */
    public void resetForRetry() {
        this.status = TranscodeQueueManager.TaskStatus.PENDING;
        this.startTime = 0;
        this.endTime = 0;
        this.errorMessage = null;
        incrementRetryCount();
    }

    /**
     * 比较器实现，用于优先级队列排序
     * 优先级数字越小，优先级越高
     */
    @Override
    public int compareTo(TranscodeTask other) {
        // 首先按优先级排序
        int priorityCompare = Integer.compare(this.priority.getLevel(), other.priority.getLevel());
        if (priorityCompare != 0) {
            return priorityCompare;
        }
        
        // 优先级相同时，按创建时间排序（先创建的先执行）
        return Long.compare(this.createTime, other.createTime);
    }

    @Override
    public String toString() {
        return String.format("TranscodeTask{taskId='%s', bizId='%s', priority=%s, status=%s, retryCount=%d}", 
                taskId, request != null ? request.getBizId() : "null", priority, status, retryCount);
    }
}
