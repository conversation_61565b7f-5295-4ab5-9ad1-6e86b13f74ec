# 统一转码配置
ydwl:
  transcoding:
    # OSS配置
    oss:
      endpoint: ${ALIYUN_OSS_ENDPOINT:}
      access-key-id: ${ALIYUN_OSS_ACCESS_KEY_ID:}
      access-key-secret: ${ALIYUN_OSS_ACCESS_KEY_SECRET:}
      bucket-name: ${ALIYUN_OSS_BUCKET:video-ydwl}
      cdn-domain: ${ALIYUN_OSS_CDN_DOMAIN:}
      callback-domain: ${ALIYUN_OSS_CALLBACK_DOMAIN:}
      callback-url: ${ALIYUN_OSS_CALLBACK_URL:}
      max-file-size: 2147483648  # 2GB

    # 阿里云配置
    aliyun:
      access-key-id: ${ALIYUN_ACCESS_KEY_ID:}
      access-key-secret: ${ALIYUN_ACCESS_KEY_SECRET:}
      region-id: ${ALIYUN_REGION_ID:cn-beijing}
      domain-name: ${ALIYUN_LIVE_DOMAIN:}
      oss-endpoint: ${ALIYUN_OSS_ENDPOINT:}
      oss-bucket: ${ALIYUN_OSS_BUCKET:}

    # 转码配置
    transcode:
      # FC转码配置
      fc:
        access-key-id: ${ALIYUN_ACCESS_KEY_ID:}
        access-key-secret: ${ALIYUN_ACCESS_KEY_SECRET:}
        oss-endpoint: ${ALIYUN_OSS_ENDPOINT:}
        oss-bucket-name: ${ALIYUN_OSS_BUCKET:video-ydwl}
        fc-endpoint: ${ALIYUN_FC_ENDPOINT:}
        transcode-complete-callback: https://yd-live.vip.cpolar.cn/live/transcode/callback
        pipeline: default-pipeline
        function-name: video-turncode-ydwl-prod
        uhd480-p: FC-480p-template
        sd720-p: FC-720p-template
        hd1080-p: FC-1080p-template

      # MTS转码配置
      mts:
        access-key-id: ${ALIYUN_ACCESS_KEY_ID:}
        access-key-secret: ${ALIYUN_ACCESS_KEY_SECRET:}
        oss-endpoint: ${ALIYUN_OSS_ENDPOINT:}
        oss-bucket-name: ${ALIYUN_OSS_BUCKET:video-ydwl}
        mts-endpoint: ${ALIYUN_MTS_ENDPOINT:}
        pipeline: ${ALIYUN_MTS_PIPELINE_ID:YOUR_DEFAULT_MTS_PIPELINE_ID}
        transcode-complete-callback: https://yd-live.vip.cpolar.cn/live/transcode/callback
        uhd480-p: YOUR_VALID_480P_TEMPLATE_ID_HERE
        sd720-p: 78b4d8f80f5c44a5b23eb07c1f585bb3
        hd1080-p: 752c184e452a41db86714fdf74601e2b
        hd2k: YOUR_VALID_2K_TEMPLATE_ID_HERE
        location: oss-cn-beijing
        large: 524288000  # 500MB
        medium: 104857600  # 100MB

      # 文件大小阈值配置
      file-threshold:
        large: 524288000  # 500MB
        medium: 104857600  # 100MB

      # 回调配置
      callback:
        default-url: https://yd-live.vip.cpolar.cn/live/transcode/callback
        fc-url: https://yd-live.vip.cpolar.cn/live/transcode/callback
        mts-url: https://yd-live.vip.cpolar.cn/live/transcode/callback

    # 直播流配置
    live-stream:
      push-domain: ${ALIYUN_LIVE_PUSH_DOMAIN:}
      play-domain: ${ALIYUN_LIVE_PLAY_DOMAIN:}
      app-name: ${ALIYUN_LIVE_APP_NAME:live}
      push-secret-key: ${ALIYUN_LIVE_PUSH_SECRET:}
      play-secret-key: ${ALIYUN_LIVE_PLAY_SECRET:}
      play-url-expire-seconds: 86400
      live-start-callback: ${ALIYUN_LIVE_START_CALLBACK:}
      live-stop-callback: ${ALIYUN_LIVE_STOP_CALLBACK:}
      live-record-callback: ${ALIYUN_LIVE_RECORD_CALLBACK:}
      live-record-complete-callback: ${ALIYUN_LIVE_RECORD_COMPLETE_CALLBACK:}