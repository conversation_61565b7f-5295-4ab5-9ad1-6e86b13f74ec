package com.ydwl.live.config.adapter;

import com.ydwl.live.config.ConfigManager;
import com.ydwl.live.config.UnifiedLiveConfig;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * 配置适配器
 * 为旧代码提供兼容性支持，避免大规模代码修改
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Configuration
@RequiredArgsConstructor
@ConditionalOnProperty(name = "ydwl.live.config.adapter.enabled", havingValue = "true", matchIfMissing = true)
public class ConfigAdapter {

    private final ConfigManager configManager;

    /**
     * 提供兼容的LiveStreamConfig Bean
     */
    @Bean
    @Primary
    public com.ydwl.live.config.LiveStreamConfig liveStreamConfig() {
        UnifiedLiveConfig.StreamConfig streamConfig = configManager.getStreamConfig();
        
        com.ydwl.live.config.LiveStreamConfig legacyConfig = new com.ydwl.live.config.LiveStreamConfig();
        legacyConfig.setPushDomain(streamConfig.getPushDomain());
        legacyConfig.setPlayDomain(streamConfig.getPlayDomain());
        legacyConfig.setAppName(streamConfig.getAppName());
        legacyConfig.setPushSecretKey(streamConfig.getPushSecretKey());
        legacyConfig.setPlaySecretKey(streamConfig.getPlaySecretKey());
        legacyConfig.setPlayUrlExpireSeconds(streamConfig.getPlayUrlExpireSeconds());
        
        return legacyConfig;
    }

    /**
     * 提供兼容的VideoProcessConfig Bean
     */
    @Bean
    @Primary
    public com.ydwl.live.config.VideoProcessConfig videoProcessConfig() {
        UnifiedLiveConfig.VideoConfig videoConfig = configManager.getVideoConfig();
        
        com.ydwl.live.config.VideoProcessConfig legacyConfig = new com.ydwl.live.config.VideoProcessConfig();
        legacyConfig.setMaxFileSize(videoConfig.getMaxFileSize());
        legacyConfig.setSupportedFormats(videoConfig.getSupportedFormats());
        legacyConfig.setMaxRetryCount(videoConfig.getMaxRetryCount());
        legacyConfig.setRetryInterval(videoConfig.getRetryInterval());
        legacyConfig.setTranscodeTimeout(videoConfig.getTranscodeTimeout());
        legacyConfig.setAsyncTranscode(videoConfig.isAsyncTranscode());
        legacyConfig.setTranscodeQueueSize(videoConfig.getTranscodeQueueSize());
        legacyConfig.setTranscodeThreadPoolSize(videoConfig.getTranscodeThreadPoolSize());
        
        return legacyConfig;
    }

    /**
     * 提供兼容的OssConfigProperties Bean
     */
    @Bean
    @Primary
    public com.ydwl.live.config.OssConfigProperties ossConfigProperties() {
        UnifiedLiveConfig.OssConfig ossConfig = configManager.getOssConfig();
        
        com.ydwl.live.config.OssConfigProperties legacyConfig = new com.ydwl.live.config.OssConfigProperties();
        legacyConfig.setAccessKey(ossConfig.getAccessKey());
        legacyConfig.setSecretKey(ossConfig.getSecretKey());
        legacyConfig.setEndpoint(ossConfig.getEndpoint());
        legacyConfig.setBucketName(ossConfig.getBucketName());
        legacyConfig.setNotifyTopic(ossConfig.getNotifyTopic());
        legacyConfig.setCallbackUrl(ossConfig.getCallbackUrl());
        legacyConfig.setMaxFileSize(ossConfig.getMaxFileSize());
        
        return legacyConfig;
    }

    /**
     * 提供兼容的LiveStreamConfigProperties Bean
     */
    @Bean
    @Primary
    public com.ydwl.live.config.LiveStreamConfigProperties liveStreamConfigProperties() {
        UnifiedLiveConfig.AliyunConfig aliyunConfig = configManager.getAliyunConfig();
        
        com.ydwl.live.config.LiveStreamConfigProperties legacyConfig = new com.ydwl.live.config.LiveStreamConfigProperties();
        legacyConfig.setAccessKeyId(aliyunConfig.getAccessKeyId());
        legacyConfig.setAccessKeySecret(aliyunConfig.getAccessKeySecret());
        legacyConfig.setRegionId(aliyunConfig.getRegionId());
        legacyConfig.setDomainName(aliyunConfig.getDomainName());
        legacyConfig.setOssEndpoint(aliyunConfig.getOssEndpoint());
        legacyConfig.setOssBucket(aliyunConfig.getOssBucket());
        
        return legacyConfig;
    }
}
