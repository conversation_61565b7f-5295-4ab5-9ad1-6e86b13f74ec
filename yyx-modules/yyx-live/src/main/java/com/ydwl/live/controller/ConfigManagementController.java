package com.ydwl.live.controller;

import com.ydwl.common.core.domain.R;
import com.ydwl.common.satoken.utils.LoginHelper;
import com.ydwl.live.config.ConfigManager;

import com.ydwl.live.config.UnifiedLiveConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 配置管理控制器
 * 提供配置查看、更新和监控功能
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Slf4j
@RestController
@RequestMapping("/live/config")
@RequiredArgsConstructor
@ConditionalOnProperty(name = "ydwl.live.config.management.enabled", havingValue = "true", matchIfMissing = false)
public class ConfigManagementController {

    private final ConfigManager configManager;

    /**
     * 获取所有配置
     */
    @GetMapping("/all")
    public R<Map<String, Object>> getAllConfigs() {
        try {
            // 检查管理员权限
            if (!LoginHelper.isAdmin()) {
                return R.fail("权限不足");
            }

            Map<String, Object> configs = configManager.getAllConfigs();
            return R.ok(configs);
        } catch (Exception e) {
            log.error("获取配置失败", e);
            return R.fail("获取配置失败: " + e.getMessage());
        }
    }

    /**
     * 获取推流配置
     */
    @GetMapping("/stream")
    public R<UnifiedLiveConfig.StreamConfig> getStreamConfig() {
        try {
            UnifiedLiveConfig.StreamConfig config = configManager.getStreamConfig();
            return R.ok(config);
        } catch (Exception e) {
            log.error("获取推流配置失败", e);
            return R.fail("获取推流配置失败: " + e.getMessage());
        }
    }

    /**
     * 获取视频处理配置
     */
    @GetMapping("/video")
    public R<UnifiedLiveConfig.VideoConfig> getVideoConfig() {
        try {
            UnifiedLiveConfig.VideoConfig config = configManager.getVideoConfig();
            return R.ok(config);
        } catch (Exception e) {
            log.error("获取视频处理配置失败", e);
            return R.fail("获取视频处理配置失败: " + e.getMessage());
        }
    }

    /**
     * 获取缓存配置
     */
    @GetMapping("/cache")
    public R<UnifiedLiveConfig.CacheConfig> getCacheConfig() {
        try {
            UnifiedLiveConfig.CacheConfig config = configManager.getCacheConfig();
            return R.ok(config);
        } catch (Exception e) {
            log.error("获取缓存配置失败", e);
            return R.fail("获取缓存配置失败: " + e.getMessage());
        }
    }

    /**
     * 获取任务配置
     */
    @GetMapping("/task")
    public R<UnifiedLiveConfig.TaskConfig> getTaskConfig() {
        try {
            UnifiedLiveConfig.TaskConfig config = configManager.getTaskConfig();
            return R.ok(config);
        } catch (Exception e) {
            log.error("获取任务配置失败", e);
            return R.fail("获取任务配置失败: " + e.getMessage());
        }
    }

    /**
     * 更新推流配置
     */
    @PostMapping("/stream")
    public R<Void> updateStreamConfig(@RequestBody UnifiedLiveConfig.StreamConfig newConfig) {
        try {
            // 检查管理员权限
            if (!LoginHelper.isAdmin()) {
                return R.fail("权限不足");
            }

            // 验证配置
            if (newConfig.getPushDomain() == null || newConfig.getPushDomain().trim().isEmpty()) {
                return R.fail("推流域名不能为空");
            }

            if (newConfig.getPlayDomain() == null || newConfig.getPlayDomain().trim().isEmpty()) {
                return R.fail("播放域名不能为空");
            }

            // 更新配置缓存
            configManager.updateConfigCache("stream", newConfig);
            
            log.info("推流配置已更新，操作用户: {}", LoginHelper.getUserId());
            return R.ok();
        } catch (Exception e) {
            log.error("更新推流配置失败", e);
            return R.fail("更新推流配置失败: " + e.getMessage());
        }
    }

    /**
     * 更新缓存配置
     */
    @PostMapping("/cache")
    public R<Void> updateCacheConfig(@RequestBody UnifiedLiveConfig.CacheConfig newConfig) {
        try {
            // 检查管理员权限
            if (!LoginHelper.isAdmin()) {
                return R.fail("权限不足");
            }

            // 验证配置
            if (newConfig.getLiveInfoCacheTime() <= 0) {
                return R.fail("直播信息缓存时间必须大于0");
            }

            if (newConfig.getStreamInfoCacheTime() <= 0) {
                return R.fail("推流信息缓存时间必须大于0");
            }

            // 更新配置缓存
            configManager.updateConfigCache("cache", newConfig);
            
            log.info("缓存配置已更新，操作用户: {}", LoginHelper.getUserId());
            return R.ok();
        } catch (Exception e) {
            log.error("更新缓存配置失败", e);
            return R.fail("更新缓存配置失败: " + e.getMessage());
        }
    }

    /**
     * 获取配置健康状态
     */
    @GetMapping("/health")
    public R<Map<String, Object>> getConfigHealth() {
        try {
            Map<String, Object> health = new HashMap<>();
            
            // 检查推流配置
            UnifiedLiveConfig.StreamConfig streamConfig = configManager.getStreamConfig();
            health.put("stream", checkStreamConfigHealth(streamConfig));
            
            // 检查OSS配置
            UnifiedLiveConfig.OssConfig ossConfig = configManager.getOssConfig();
            health.put("oss", checkOssConfigHealth(ossConfig));
            
            // 检查阿里云配置
            UnifiedLiveConfig.AliyunConfig aliyunConfig = configManager.getAliyunConfig();
            health.put("aliyun", checkAliyunConfigHealth(aliyunConfig));
            
            // 检查视频处理配置
            UnifiedLiveConfig.VideoConfig videoConfig = configManager.getVideoConfig();
            health.put("video", checkVideoConfigHealth(videoConfig));
            
            return R.ok(health);
        } catch (Exception e) {
            log.error("获取配置健康状态失败", e);
            return R.fail("获取配置健康状态失败: " + e.getMessage());
        }
    }



    /**
     * 检查推流配置健康状态
     */
    private Map<String, Object> checkStreamConfigHealth(UnifiedLiveConfig.StreamConfig config) {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "healthy");
        
        if (config.getPushDomain() == null || config.getPushDomain().trim().isEmpty()) {
            health.put("status", "unhealthy");
            health.put("issue", "推流域名为空");
        }
        
        if (config.getPlayDomain() == null || config.getPlayDomain().trim().isEmpty()) {
            health.put("status", "unhealthy");
            health.put("issue", "播放域名为空");
        }
        
        if (config.getPushSecretKey() == null || config.getPushSecretKey().trim().isEmpty()) {
            health.put("status", "warning");
            health.put("issue", "推流鉴权密钥为空，可能影响安全性");
        }
        
        return health;
    }

    /**
     * 检查OSS配置健康状态
     */
    private Map<String, Object> checkOssConfigHealth(UnifiedLiveConfig.OssConfig config) {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "healthy");
        
        if (config.getAccessKey() == null || config.getAccessKey().trim().isEmpty()) {
            health.put("status", "warning");
            health.put("issue", "OSS AccessKey为空");
        }
        
        if (config.getSecretKey() == null || config.getSecretKey().trim().isEmpty()) {
            health.put("status", "warning");
            health.put("issue", "OSS SecretKey为空");
        }
        
        return health;
    }

    /**
     * 检查阿里云配置健康状态
     */
    private Map<String, Object> checkAliyunConfigHealth(UnifiedLiveConfig.AliyunConfig config) {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "healthy");
        
        if (config.getAccessKeyId() == null || config.getAccessKeyId().trim().isEmpty()) {
            health.put("status", "warning");
            health.put("issue", "阿里云AccessKeyId为空");
        }
        
        if (config.getAccessKeySecret() == null || config.getAccessKeySecret().trim().isEmpty()) {
            health.put("status", "warning");
            health.put("issue", "阿里云AccessKeySecret为空");
        }
        
        return health;
    }

    /**
     * 检查视频处理配置健康状态
     */
    private Map<String, Object> checkVideoConfigHealth(UnifiedLiveConfig.VideoConfig config) {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "healthy");
        
        if (config.getMaxFileSize() <= 0) {
            health.put("status", "unhealthy");
            health.put("issue", "最大文件大小必须大于0");
        }
        
        if (config.getTranscodeThreadPoolSize() <= 0) {
            health.put("status", "unhealthy");
            health.put("issue", "转码线程池大小必须大于0");
        }
        
        return health;
    }
}
