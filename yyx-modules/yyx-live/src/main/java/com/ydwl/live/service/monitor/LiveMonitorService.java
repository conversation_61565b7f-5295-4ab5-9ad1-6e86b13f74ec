package com.ydwl.live.service.monitor;

import com.ydwl.live.config.ConfigManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 直播监控服务
 * 监控直播系统的各项指标和健康状态
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LiveMonitorService {

    private final ConfigManager configManager;
    private final AlertService alertService;

    // 监控指标
    private final AtomicLong totalLiveCount = new AtomicLong(0);
    private final AtomicLong activeLiveCount = new AtomicLong(0);
    private final AtomicLong totalStreamCount = new AtomicLong(0);
    private final AtomicLong activeStreamCount = new AtomicLong(0);
    private final AtomicLong totalTranscodeCount = new AtomicLong(0);
    private final AtomicLong failedTranscodeCount = new AtomicLong(0);

    // 性能指标
    private final Map<String, Double> performanceMetrics = new HashMap<>();
    
    // 告警阈值
    private static final double STREAM_FAILURE_RATE_THRESHOLD = 0.1; // 10%
    private static final double TRANSCODE_FAILURE_RATE_THRESHOLD = 0.05; // 5%
    private static final long MAX_STREAM_DURATION_HOURS = 12; // 12小时

    /**
     * 定时收集监控指标
     */
    @Scheduled(fixedRate = 60000) // 每分钟执行一次
    public void collectMetrics() {
        try {
            log.debug("开始收集监控指标");
            
            // 收集直播指标
            collectLiveMetrics();
            
            // 收集推流指标
            collectStreamMetrics();
            
            // 收集转码指标
            collectTranscodeMetrics();
            
            // 收集性能指标
            collectPerformanceMetrics();
            
            // 检查告警条件
            checkAlertConditions();
            
        } catch (Exception e) {
            log.error("收集监控指标失败", e);
        }
    }

    /**
     * 收集直播指标
     */
    private void collectLiveMetrics() {
        // 这里应该从数据库查询实际数据
        // 为了示例，使用模拟数据
        
        // 总直播数量
        long total = queryTotalLiveCount();
        totalLiveCount.set(total);
        
        // 活跃直播数量
        long active = queryActiveLiveCount();
        activeLiveCount.set(active);
        
        log.debug("直播指标 - 总数: {}, 活跃: {}", total, active);
    }

    /**
     * 收集推流指标
     */
    private void collectStreamMetrics() {
        // 总推流数量
        long total = queryTotalStreamCount();
        totalStreamCount.set(total);
        
        // 活跃推流数量
        long active = queryActiveStreamCount();
        activeStreamCount.set(active);
        
        // 推流失败率
        double failureRate = calculateStreamFailureRate();
        performanceMetrics.put("stream_failure_rate", failureRate);
        
        log.debug("推流指标 - 总数: {}, 活跃: {}, 失败率: {}", total, active, failureRate);
    }

    /**
     * 收集转码指标
     */
    private void collectTranscodeMetrics() {
        // 总转码数量
        long total = queryTotalTranscodeCount();
        totalTranscodeCount.set(total);
        
        // 失败转码数量
        long failed = queryFailedTranscodeCount();
        failedTranscodeCount.set(failed);
        
        // 转码失败率
        double failureRate = total > 0 ? (double) failed / total : 0;
        performanceMetrics.put("transcode_failure_rate", failureRate);
        
        // 平均转码时间
        double avgTime = queryAverageTranscodeTime();
        performanceMetrics.put("avg_transcode_time", avgTime);
        
        log.debug("转码指标 - 总数: {}, 失败: {}, 失败率: {}, 平均时间: {}s", 
                total, failed, failureRate, avgTime);
    }

    /**
     * 收集性能指标
     */
    private void collectPerformanceMetrics() {
        // 系统CPU使用率
        double cpuUsage = getSystemCpuUsage();
        performanceMetrics.put("cpu_usage", cpuUsage);
        
        // 系统内存使用率
        double memoryUsage = getSystemMemoryUsage();
        performanceMetrics.put("memory_usage", memoryUsage);
        
        // JVM堆内存使用率
        double heapUsage = getJvmHeapUsage();
        performanceMetrics.put("heap_usage", heapUsage);
        
        log.debug("性能指标 - CPU: {}%, 内存: {}%, 堆内存: {}%", 
                cpuUsage * 100, memoryUsage * 100, heapUsage * 100);
    }

    /**
     * 检查告警条件
     */
    private void checkAlertConditions() {
        // 检查推流失败率
        Double streamFailureRate = performanceMetrics.get("stream_failure_rate");
        if (streamFailureRate != null && streamFailureRate > STREAM_FAILURE_RATE_THRESHOLD) {
            alertService.sendAlert(AlertService.AlertType.HIGH, 
                    "推流失败率过高", 
                    String.format("当前推流失败率: %.2f%%, 阈值: %.2f%%", 
                            streamFailureRate * 100, STREAM_FAILURE_RATE_THRESHOLD * 100));
        }
        
        // 检查转码失败率
        Double transcodeFailureRate = performanceMetrics.get("transcode_failure_rate");
        if (transcodeFailureRate != null && transcodeFailureRate > TRANSCODE_FAILURE_RATE_THRESHOLD) {
            alertService.sendAlert(AlertService.AlertType.HIGH, 
                    "转码失败率过高", 
                    String.format("当前转码失败率: %.2f%%, 阈值: %.2f%%", 
                            transcodeFailureRate * 100, TRANSCODE_FAILURE_RATE_THRESHOLD * 100));
        }
        
        // 检查系统资源使用率
        Double cpuUsage = performanceMetrics.get("cpu_usage");
        if (cpuUsage != null && cpuUsage > 0.8) { // 80%
            alertService.sendAlert(AlertService.AlertType.MEDIUM, 
                    "CPU使用率过高", 
                    String.format("当前CPU使用率: %.2f%%", cpuUsage * 100));
        }
        
        Double memoryUsage = performanceMetrics.get("memory_usage");
        if (memoryUsage != null && memoryUsage > 0.85) { // 85%
            alertService.sendAlert(AlertService.AlertType.MEDIUM, 
                    "内存使用率过高", 
                    String.format("当前内存使用率: %.2f%%", memoryUsage * 100));
        }
    }

    /**
     * 获取监控报告
     */
    public MonitorReport getMonitorReport() {
        MonitorReport report = new MonitorReport();
        report.setTimestamp(LocalDateTime.now());
        report.setTotalLiveCount(totalLiveCount.get());
        report.setActiveLiveCount(activeLiveCount.get());
        report.setTotalStreamCount(totalStreamCount.get());
        report.setActiveStreamCount(activeStreamCount.get());
        report.setTotalTranscodeCount(totalTranscodeCount.get());
        report.setFailedTranscodeCount(failedTranscodeCount.get());
        report.setPerformanceMetrics(new HashMap<>(performanceMetrics));
        return report;
    }

    // 以下是模拟的查询方法，实际应该从数据库查询
    private long queryTotalLiveCount() {
        // 从数据库查询总直播数量
        return 1000; // 模拟数据
    }

    private long queryActiveLiveCount() {
        // 从数据库查询活跃直播数量
        return 50; // 模拟数据
    }

    private long queryTotalStreamCount() {
        // 从数据库查询总推流数量
        return 1200; // 模拟数据
    }

    private long queryActiveStreamCount() {
        // 从数据库查询活跃推流数量
        return 45; // 模拟数据
    }

    private double calculateStreamFailureRate() {
        // 计算推流失败率
        return 0.05; // 模拟数据：5%
    }

    private long queryTotalTranscodeCount() {
        // 从数据库查询总转码数量
        return 800; // 模拟数据
    }

    private long queryFailedTranscodeCount() {
        // 从数据库查询失败转码数量
        return 20; // 模拟数据
    }

    private double queryAverageTranscodeTime() {
        // 查询平均转码时间（秒）
        return 120.5; // 模拟数据：120.5秒
    }

    private double getSystemCpuUsage() {
        // 获取系统CPU使用率
        return 0.65; // 模拟数据：65%
    }

    private double getSystemMemoryUsage() {
        // 获取系统内存使用率
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        return (double) (totalMemory - freeMemory) / totalMemory;
    }

    private double getJvmHeapUsage() {
        // 获取JVM堆内存使用率
        Runtime runtime = Runtime.getRuntime();
        long maxMemory = runtime.maxMemory();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        return (double) usedMemory / maxMemory;
    }

    /**
     * 监控报告类
     */
    public static class MonitorReport {
        private LocalDateTime timestamp;
        private long totalLiveCount;
        private long activeLiveCount;
        private long totalStreamCount;
        private long activeStreamCount;
        private long totalTranscodeCount;
        private long failedTranscodeCount;
        private Map<String, Double> performanceMetrics;

        // Getters and Setters
        public LocalDateTime getTimestamp() { return timestamp; }
        public void setTimestamp(LocalDateTime timestamp) { this.timestamp = timestamp; }
        public long getTotalLiveCount() { return totalLiveCount; }
        public void setTotalLiveCount(long totalLiveCount) { this.totalLiveCount = totalLiveCount; }
        public long getActiveLiveCount() { return activeLiveCount; }
        public void setActiveLiveCount(long activeLiveCount) { this.activeLiveCount = activeLiveCount; }
        public long getTotalStreamCount() { return totalStreamCount; }
        public void setTotalStreamCount(long totalStreamCount) { this.totalStreamCount = totalStreamCount; }
        public long getActiveStreamCount() { return activeStreamCount; }
        public void setActiveStreamCount(long activeStreamCount) { this.activeStreamCount = activeStreamCount; }
        public long getTotalTranscodeCount() { return totalTranscodeCount; }
        public void setTotalTranscodeCount(long totalTranscodeCount) { this.totalTranscodeCount = totalTranscodeCount; }
        public long getFailedTranscodeCount() { return failedTranscodeCount; }
        public void setFailedTranscodeCount(long failedTranscodeCount) { this.failedTranscodeCount = failedTranscodeCount; }
        public Map<String, Double> getPerformanceMetrics() { return performanceMetrics; }
        public void setPerformanceMetrics(Map<String, Double> performanceMetrics) { this.performanceMetrics = performanceMetrics; }
    }
}
