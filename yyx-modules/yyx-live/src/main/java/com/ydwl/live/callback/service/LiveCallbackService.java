package com.ydwl.live.callback.service;

import com.ydwl.live.callback.dto.LiveCallbackDto;
import com.ydwl.live.config.ConfigManager;
import com.ydwl.live.service.impl.LiveStateManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 直播回调服务
 * 处理直播相关的回调业务逻辑
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LiveCallbackService {

    private final ConfigManager configManager;
    private final LiveStateManager stateManager;

    /**
     * 处理推流开始回调
     */
    public void handleStreamStart(LiveCallbackDto callbackDto) {
        log.info("处理推流开始回调 - Stream: {}, Domain: {}, App: {}", 
                callbackDto.getStream(), callbackDto.getDomain(), callbackDto.getApp());

        try {
            String liveId = callbackDto.getLiveId();
            if (liveId != null) {
                // 更新直播状态为直播中
                boolean success = stateManager.updateLiveStatus(Long.valueOf(liveId), LiveStateManager.LiveStatus.LIVE);
                if (success) {
                    log.info("推流开始，直播状态已更新为LIVE，LiveId: {}", liveId);
                } else {
                    log.warn("推流开始，但直播状态更新失败，LiveId: {}", liveId);
                }
            }

            // 这里可以添加其他业务逻辑
            // 比如：发送通知、更新统计数据等

        } catch (Exception e) {
            log.error("处理推流开始回调失败", e);
        }
    }

    /**
     * 处理推流结束回调
     */
    public void handleStreamStop(LiveCallbackDto callbackDto) {
        log.info("处理推流结束回调 - Stream: {}, Duration: {}s", 
                callbackDto.getStream(), callbackDto.getDuration());

        try {
            String liveId = callbackDto.getLiveId();
            if (liveId != null) {
                // 更新直播状态为已结束
                boolean success = stateManager.updateLiveStatus(Long.valueOf(liveId), LiveStateManager.LiveStatus.ENDED);
                if (success) {
                    log.info("推流结束，直播状态已更新为ENDED，LiveId: {}, 时长: {}s", 
                            liveId, callbackDto.getDuration());
                } else {
                    log.warn("推流结束，但直播状态更新失败，LiveId: {}", liveId);
                }
            }

            // 这里可以添加其他业务逻辑
            // 比如：生成直播报告、清理资源等

        } catch (Exception e) {
            log.error("处理推流结束回调失败", e);
        }
    }

    /**
     * 处理录制开始回调
     */
    public void handleRecordStart(LiveCallbackDto callbackDto) {
        log.info("处理录制开始回调 - Stream: {}, FileName: {}", 
                callbackDto.getStream(), callbackDto.getFileName());

        try {
            // 这里可以添加录制开始的业务逻辑
            // 比如：记录录制信息、发送通知等

        } catch (Exception e) {
            log.error("处理录制开始回调失败", e);
        }
    }

    /**
     * 处理录制结束回调
     */
    public void handleRecordStop(LiveCallbackDto callbackDto) {
        log.info("处理录制结束回调 - Stream: {}, FileName: {}, FileSize: {}, FileUrl: {}", 
                callbackDto.getStream(), callbackDto.getFileName(), 
                callbackDto.getFileSize(), callbackDto.getFileUrl());

        try {
            String liveId = callbackDto.getLiveId();
            if (liveId != null) {
                // 创建回放记录
                createReplayRecord(liveId, callbackDto);
                
                // 更新直播状态为回放中（如果当前是已结束状态）
                LiveStateManager.LiveStatus currentStatus = stateManager.getCurrentStatus(Long.valueOf(liveId));
                if (currentStatus == LiveStateManager.LiveStatus.ENDED) {
                    stateManager.updateLiveStatus(Long.valueOf(liveId), LiveStateManager.LiveStatus.REPLAY);
                    log.info("录制完成，直播状态已更新为REPLAY，LiveId: {}", liveId);
                }
            }

        } catch (Exception e) {
            log.error("处理录制结束回调失败", e);
        }
    }

    /**
     * 处理截图回调
     */
    public void handleSnapshot(LiveCallbackDto callbackDto) {
        log.info("处理截图回调 - Stream: {}, SnapshotUrl: {}", 
                callbackDto.getStream(), callbackDto.getSnapshotUrl());

        try {
            // 这里可以添加截图处理的业务逻辑
            // 比如：保存截图信息、生成封面等

        } catch (Exception e) {
            log.error("处理截图回调失败", e);
        }
    }

    /**
     * 判断是否需要录制
     */
    public boolean shouldRecord(String streamId) {
        try {
            // 这里可以根据业务规则判断是否需要录制
            // 比如：检查直播设置、用户权限等
            
            log.info("查询是否需要录制 - StreamId: {}", streamId);
            
            // 默认需要录制
            return true;
            
        } catch (Exception e) {
            log.error("查询录制需求失败，StreamId: {}", streamId, e);
            // 出错时默认不录制
            return false;
        }
    }

    /**
     * 创建回放记录
     */
    private void createReplayRecord(String liveId, LiveCallbackDto callbackDto) {
        try {
            log.info("创建回放记录 - LiveId: {}, FileUrl: {}", liveId, callbackDto.getFileUrl());
            
            // 这里应该调用回放服务创建回放记录
            // 包括：文件URL、文件大小、时长等信息
            
            // 示例代码（需要根据实际的回放服务接口调整）
            /*
            LiveReplayBo replayBo = new LiveReplayBo();
            replayBo.setLiveId(Long.valueOf(liveId));
            replayBo.setFileName(callbackDto.getFileName());
            replayBo.setFileUrl(callbackDto.getFileUrl());
            replayBo.setFileSize(callbackDto.getFileSize());
            replayBo.setDuration(callbackDto.getDuration());
            replayBo.setBucket(callbackDto.getBucket());
            replayBo.setObject(callbackDto.getObject());
            
            liveReplayService.insertByBo(replayBo);
            */
            
        } catch (Exception e) {
            log.error("创建回放记录失败，LiveId: {}", liveId, e);
        }
    }
}
