package com.ydwl.live.callback.dto;

import lombok.Data;

import java.util.Map;

/**
 * 直播回调数据传输对象
 * 标准化直播回调数据结构
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Data
public class LiveCallbackDto {

    /**
     * 回调动作类型
     */
    private String action;

    /**
     * 事件类型（兼容字段）
     */
    private String event;

    /**
     * 推流域名
     */
    private String domain;

    /**
     * 应用名称
     */
    private String app;

    /**
     * 流名称
     */
    private String stream;

    /**
     * 推流URL
     */
    private String url;

    /**
     * 客户端IP
     */
    private String clientIp;

    /**
     * 服务器IP
     */
    private String serverIp;

    /**
     * 推流开始时间（Unix时间戳）
     */
    private Long startTime;

    /**
     * 推流结束时间（Unix时间戳）
     */
    private Long endTime;

    /**
     * 推流时长（秒）
     */
    private Long duration;

    /**
     * 录制文件名
     */
    private String fileName;

    /**
     * 录制文件大小（字节）
     */
    private Long fileSize;

    /**
     * 录制文件URL
     */
    private String fileUrl;

    /**
     * OSS存储桶
     */
    private String bucket;

    /**
     * OSS对象键
     */
    private String object;

    /**
     * 截图URL
     */
    private String snapshotUrl;

    /**
     * 截图时间
     */
    private Long snapshotTime;

    /**
     * 扩展参数
     */
    private Map<String, Object> extra;

    /**
     * 从Map创建LiveCallbackDto
     */
    public static LiveCallbackDto fromMap(Map<String, Object> data) {
        LiveCallbackDto dto = new LiveCallbackDto();
        
        dto.setAction((String) data.get("action"));
        dto.setEvent((String) data.get("event"));
        dto.setDomain((String) data.get("domain"));
        dto.setApp((String) data.get("app"));
        dto.setStream((String) data.get("stream"));
        dto.setUrl((String) data.get("url"));
        dto.setClientIp((String) data.get("client_ip"));
        dto.setServerIp((String) data.get("server_ip"));
        
        // 处理时间字段
        Object startTimeObj = data.get("start_time");
        if (startTimeObj != null) {
            dto.setStartTime(Long.valueOf(startTimeObj.toString()));
        }
        
        Object endTimeObj = data.get("end_time");
        if (endTimeObj != null) {
            dto.setEndTime(Long.valueOf(endTimeObj.toString()));
        }
        
        Object durationObj = data.get("duration");
        if (durationObj != null) {
            dto.setDuration(Long.valueOf(durationObj.toString()));
        }
        
        // 处理录制相关字段
        dto.setFileName((String) data.get("file_name"));
        dto.setFileUrl((String) data.get("file_url"));
        dto.setBucket((String) data.get("bucket"));
        dto.setObject((String) data.get("object"));
        
        Object fileSizeObj = data.get("file_size");
        if (fileSizeObj != null) {
            dto.setFileSize(Long.valueOf(fileSizeObj.toString()));
        }
        
        // 处理截图相关字段
        dto.setSnapshotUrl((String) data.get("snapshot_url"));
        Object snapshotTimeObj = data.get("snapshot_time");
        if (snapshotTimeObj != null) {
            dto.setSnapshotTime(Long.valueOf(snapshotTimeObj.toString()));
        }
        
        // 保存原始数据作为扩展参数
        dto.setExtra(data);
        
        return dto;
    }

    /**
     * 获取流ID（从流名称中提取）
     */
    public String getStreamId() {
        if (stream != null && stream.contains("_")) {
            // 假设流名称格式为：live_123 或 stream_456
            String[] parts = stream.split("_");
            if (parts.length > 1) {
                return parts[1];
            }
        }
        return stream;
    }

    /**
     * 获取直播ID（从流名称中提取）
     */
    public String getLiveId() {
        // 这里可以根据实际的流名称规则来提取直播ID
        return getStreamId();
    }

    /**
     * 判断是否为推流开始事件
     */
    public boolean isStreamStart() {
        return "publish".equalsIgnoreCase(action) || 
               "stream_start".equalsIgnoreCase(action) ||
               "publish".equalsIgnoreCase(event) ||
               "stream_start".equalsIgnoreCase(event);
    }

    /**
     * 判断是否为推流结束事件
     */
    public boolean isStreamStop() {
        return "publish_done".equalsIgnoreCase(action) || 
               "stream_stop".equalsIgnoreCase(action) ||
               "publish_done".equalsIgnoreCase(event) ||
               "stream_stop".equalsIgnoreCase(event);
    }

    /**
     * 判断是否为录制开始事件
     */
    public boolean isRecordStart() {
        return "record_start".equalsIgnoreCase(action) ||
               "record_start".equalsIgnoreCase(event);
    }

    /**
     * 判断是否为录制结束事件
     */
    public boolean isRecordStop() {
        return "record_stop".equalsIgnoreCase(action) ||
               "record_stop".equalsIgnoreCase(event);
    }

    /**
     * 判断是否为截图事件
     */
    public boolean isSnapshot() {
        return "snapshot".equalsIgnoreCase(action) ||
               "snapshot".equalsIgnoreCase(event);
    }

    @Override
    public String toString() {
        return String.format("LiveCallbackDto{action='%s', domain='%s', app='%s', stream='%s'}", 
                action, domain, app, stream);
    }
}
