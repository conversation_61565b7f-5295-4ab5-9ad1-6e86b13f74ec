package com.ydwl.live.listener;

import com.ydwl.live.event.ConfigChangeEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 配置变更监听器
 * 监听配置变更事件并执行相应的处理逻辑
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ConfigChangeListener {

    /**
     * 处理配置变更事件
     */
    @Async
    @EventListener
    public void handleConfigChange(ConfigChangeEvent event) {
        log.info("收到配置变更事件: {}", event);

        try {
            switch (event.getConfigType()) {
                case "stream":
                    handleStreamConfigChange(event);
                    break;
                case "cache":
                    handleCacheConfigChange(event);
                    break;
                case "video":
                    handleVideoConfigChange(event);
                    break;
                case "task":
                    handleTaskConfigChange(event);
                    break;
                default:
                    log.info("未知的配置类型: {}", event.getConfigType());
                    break;
            }
        } catch (Exception e) {
            log.error("处理配置变更事件失败: {}", event, e);
        }
    }

    /**
     * 处理推流配置变更
     */
    private void handleStreamConfigChange(ConfigChangeEvent event) {
        log.info("推流配置已变更，配置键: {}, 操作用户: {}", event.getConfigKey(), event.getOperatorId());
        
        // 这里可以添加推流配置变更后的处理逻辑
        // 例如：刷新推流地址、通知相关服务等
        
        switch (event.getConfigKey()) {
            case "pushDomain":
                log.info("推流域名已变更: {} -> {}", event.getOldValue(), event.getNewValue());
                // 可以在这里添加推流域名变更后的处理逻辑
                break;
            case "playDomain":
                log.info("播放域名已变更: {} -> {}", event.getOldValue(), event.getNewValue());
                // 可以在这里添加播放域名变更后的处理逻辑
                break;
            case "pushSecretKey":
                log.info("推流鉴权密钥已变更，操作用户: {}", event.getOperatorId());
                // 出于安全考虑，不记录密钥的具体值
                break;
            default:
                log.info("其他推流配置变更: {}", event.getConfigKey());
                break;
        }
    }

    /**
     * 处理缓存配置变更
     */
    private void handleCacheConfigChange(ConfigChangeEvent event) {
        log.info("缓存配置已变更，配置键: {}, 操作用户: {}", event.getConfigKey(), event.getOperatorId());
        
        // 这里可以添加缓存配置变更后的处理逻辑
        // 例如：清理缓存、重新配置缓存管理器等
        
        switch (event.getConfigKey()) {
            case "enabled":
                boolean newEnabled = (Boolean) event.getNewValue();
                log.info("缓存启用状态已变更: {}", newEnabled);
                if (!newEnabled) {
                    // 如果禁用缓存，可以在这里清理所有缓存
                    log.info("缓存已禁用，建议清理现有缓存");
                }
                break;
            case "liveInfoCacheTime":
                log.info("直播信息缓存时间已变更: {} -> {}", event.getOldValue(), event.getNewValue());
                break;
            case "streamInfoCacheTime":
                log.info("推流信息缓存时间已变更: {} -> {}", event.getOldValue(), event.getNewValue());
                break;
            default:
                log.info("其他缓存配置变更: {}", event.getConfigKey());
                break;
        }
    }

    /**
     * 处理视频处理配置变更
     */
    private void handleVideoConfigChange(ConfigChangeEvent event) {
        log.info("视频处理配置已变更，配置键: {}, 操作用户: {}", event.getConfigKey(), event.getOperatorId());
        
        // 这里可以添加视频处理配置变更后的处理逻辑
        // 例如：重新配置转码参数、调整线程池大小等
        
        switch (event.getConfigKey()) {
            case "maxFileSize":
                log.info("最大文件大小已变更: {} -> {}", event.getOldValue(), event.getNewValue());
                break;
            case "transcodeThreadPoolSize":
                log.info("转码线程池大小已变更: {} -> {}", event.getOldValue(), event.getNewValue());
                // 可以在这里重新配置线程池
                break;
            case "asyncTranscode":
                boolean newAsync = (Boolean) event.getNewValue();
                log.info("异步转码设置已变更: {}", newAsync);
                break;
            default:
                log.info("其他视频处理配置变更: {}", event.getConfigKey());
                break;
        }
    }

    /**
     * 处理任务配置变更
     */
    private void handleTaskConfigChange(ConfigChangeEvent event) {
        log.info("任务配置已变更，配置键: {}, 操作用户: {}", event.getConfigKey(), event.getOperatorId());
        
        // 这里可以添加任务配置变更后的处理逻辑
        // 例如：重新配置定时任务、调整检查间隔等
        
        switch (event.getConfigKey()) {
            case "enabled":
                boolean newEnabled = (Boolean) event.getNewValue();
                log.info("定时任务启用状态已变更: {}", newEnabled);
                break;
            case "statusCheckInterval":
                log.info("状态检查间隔已变更: {} -> {}", event.getOldValue(), event.getNewValue());
                // 可以在这里重新配置定时任务的执行间隔
                break;
            case "recordCheckInterval":
                log.info("录制检查间隔已变更: {} -> {}", event.getOldValue(), event.getNewValue());
                break;
            default:
                log.info("其他任务配置变更: {}", event.getConfigKey());
                break;
        }
    }
}
