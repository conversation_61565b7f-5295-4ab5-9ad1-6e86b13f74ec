package com.ydwl.live.service.stream;

import com.ydwl.live.domain.LiveStream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 推流健康检查器
 * 检查推流的健康状态
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class StreamHealthChecker {

    /**
     * 检查推流健康状态
     *
     * @param stream 推流信息
     * @return 健康状态
     */
    public StreamMonitorService.StreamHealthStatus checkStreamHealth(LiveStream stream) {
        try {
            log.debug("检查推流健康状态，StreamId: {}", stream.getId());

            // 检查推流状态
            if (stream.getStreamStatus() == null) {
                return new StreamMonitorService.StreamHealthStatus(
                    StreamMonitorService.StreamHealthStatus.Status.ERROR, 
                    "推流状态为空"
                );
            }

            // 根据推流状态判断健康状况
            switch (stream.getStreamStatus().intValue()) {
                case 1: // 正常状态
                    return checkActiveStreamHealth(stream);
                case 2: // 异常状态
                    return new StreamMonitorService.StreamHealthStatus(
                        StreamMonitorService.StreamHealthStatus.Status.ERROR, 
                        "推流处于异常状态"
                    );
                case 3: // 警告状态
                    return new StreamMonitorService.StreamHealthStatus(
                        StreamMonitorService.StreamHealthStatus.Status.UNSTABLE, 
                        "推流处于警告状态"
                    );
                default:
                    return new StreamMonitorService.StreamHealthStatus(
                        StreamMonitorService.StreamHealthStatus.Status.DISCONNECTED, 
                        "推流状态未知: " + stream.getStreamStatus()
                    );
            }

        } catch (Exception e) {
            log.error("检查推流健康状态失败，StreamId: {}", stream.getId(), e);
            StreamMonitorService.StreamHealthStatus status = new StreamMonitorService.StreamHealthStatus(
                StreamMonitorService.StreamHealthStatus.Status.ERROR, 
                "健康检查异常: " + e.getMessage()
            );
            status.setCriticalError(true);
            return status;
        }
    }

    /**
     * 检查活跃推流的健康状态
     */
    private StreamMonitorService.StreamHealthStatus checkActiveStreamHealth(LiveStream stream) {
        // 检查推流是否长时间运行
        if (stream.getCreateTime() != null) {
            long runningHours = (System.currentTimeMillis() - stream.getCreateTime().getTime()) / (1000 * 60 * 60);
            if (runningHours > 12) { // 超过12小时
                StreamMonitorService.StreamHealthStatus status = new StreamMonitorService.StreamHealthStatus(
                    StreamMonitorService.StreamHealthStatus.Status.UNSTABLE, 
                    "推流运行时间过长: " + runningHours + "小时"
                );
                status.setBitrate(simulateBitrate());
                status.setFrameRate(simulateFrameRate());
                return status;
            }
        }

        // 模拟检查推流质量指标
        double bitrate = simulateBitrate();
        double frameRate = simulateFrameRate();

        StreamMonitorService.StreamHealthStatus status;
        if (bitrate < 500 || frameRate < 15) {
            // 码率或帧率过低
            status = new StreamMonitorService.StreamHealthStatus(
                StreamMonitorService.StreamHealthStatus.Status.UNSTABLE, 
                String.format("推流质量不佳，码率: %.1fkbps, 帧率: %.1ffps", bitrate, frameRate)
            );
        } else {
            // 推流正常
            status = new StreamMonitorService.StreamHealthStatus(
                StreamMonitorService.StreamHealthStatus.Status.HEALTHY, 
                "推流状态正常"
            );
        }

        status.setBitrate(bitrate);
        status.setFrameRate(frameRate);
        return status;
    }

    /**
     * 模拟码率检测
     * 实际应该调用阿里云API获取真实数据
     */
    private double simulateBitrate() {
        // 模拟码率：800-2000 kbps
        return 800 + Math.random() * 1200;
    }

    /**
     * 模拟帧率检测
     * 实际应该调用阿里云API获取真实数据
     */
    private double simulateFrameRate() {
        // 模拟帧率：20-30 fps
        return 20 + Math.random() * 10;
    }
}
