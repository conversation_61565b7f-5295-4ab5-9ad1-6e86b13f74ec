package com.ydwl.live.config;

import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import java.util.HashMap;
import java.util.Map;

/**
 * 配置管理器
 * 提供统一的配置访问接口和配置验证功能
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ConfigManager {

    private final UnifiedLiveConfig unifiedLiveConfig;

    /**
     * 配置缓存
     */
    private final Map<String, Object> configCache = new HashMap<>();

    @PostConstruct
    public void init() {
        log.info("配置管理器初始化开始");
        validateConfigurations();
        cacheConfigurations();
        log.info("配置管理器初始化完成");
    }

    @EventListener(ApplicationReadyEvent.class)
    public void onApplicationReady() {
        printConfigurationSummary();
    }

    /**
     * 验证配置
     */
    private void validateConfigurations() {
        log.info("开始验证配置...");

        // 验证推流配置
        validateStreamConfig();

        // 验证OSS配置
        validateOssConfig();

        // 验证阿里云配置
        validateAliyunConfig();

        // 验证视频处理配置
        validateVideoConfig();

        log.info("配置验证完成");
    }

    /**
     * 缓存配置
     */
    private void cacheConfigurations() {
        configCache.put("stream", unifiedLiveConfig.getStream());
        configCache.put("task", unifiedLiveConfig.getTask());
        configCache.put("auth", unifiedLiveConfig.getAuth());
        configCache.put("video", unifiedLiveConfig.getVideo());
        configCache.put("cache", unifiedLiveConfig.getCache());
        configCache.put("oss", unifiedLiveConfig.getOss());
        configCache.put("aliyun", unifiedLiveConfig.getAliyun());
        configCache.put("wxMiniapp", unifiedLiveConfig.getWxMiniapp());
    }

    /**
     * 获取推流配置
     */
    public UnifiedLiveConfig.StreamConfig getStreamConfig() {
        return (UnifiedLiveConfig.StreamConfig) configCache.get("stream");
    }

    /**
     * 获取任务配置
     */
    public UnifiedLiveConfig.TaskConfig getTaskConfig() {
        return (UnifiedLiveConfig.TaskConfig) configCache.get("task");
    }

    /**
     * 获取认证配置
     */
    public UnifiedLiveConfig.AuthConfig getAuthConfig() {
        return (UnifiedLiveConfig.AuthConfig) configCache.get("auth");
    }

    /**
     * 获取视频处理配置
     */
    public UnifiedLiveConfig.VideoConfig getVideoConfig() {
        return (UnifiedLiveConfig.VideoConfig) configCache.get("video");
    }

    /**
     * 获取缓存配置
     */
    public UnifiedLiveConfig.CacheConfig getCacheConfig() {
        return (UnifiedLiveConfig.CacheConfig) configCache.get("cache");
    }

    /**
     * 获取OSS配置
     */
    public UnifiedLiveConfig.OssConfig getOssConfig() {
        return (UnifiedLiveConfig.OssConfig) configCache.get("oss");
    }

    /**
     * 获取阿里云配置
     */
    public UnifiedLiveConfig.AliyunConfig getAliyunConfig() {
        return (UnifiedLiveConfig.AliyunConfig) configCache.get("aliyun");
    }

    /**
     * 获取微信小程序配置
     */
    public UnifiedLiveConfig.WxMiniappConfig getWxMiniappConfig() {
        return (UnifiedLiveConfig.WxMiniappConfig) configCache.get("wxMiniapp");
    }

    /**
     * 验证推流配置
     */
    private void validateStreamConfig() {
        UnifiedLiveConfig.StreamConfig config = unifiedLiveConfig.getStream();

        if (config.getPushDomain() == null || config.getPushDomain().trim().isEmpty()) {
            throw new IllegalArgumentException("推流域名不能为空");
        }

        if (config.getPlayDomain() == null || config.getPlayDomain().trim().isEmpty()) {
            throw new IllegalArgumentException("播放域名不能为空");
        }

        if (config.getAppName() == null || config.getAppName().trim().isEmpty()) {
            throw new IllegalArgumentException("应用名称不能为空");
        }

        if (config.getPushSecretKey() == null || config.getPushSecretKey().trim().isEmpty()) {
            log.warn("推流鉴权密钥为空，可能影响安全性");
        }

        if (config.getPlayUrlExpireSeconds() <= 0) {
            throw new IllegalArgumentException("播放URL有效期必须大于0");
        }

        log.info("推流配置验证通过");
    }

    /**
     * 验证OSS配置
     */
    private void validateOssConfig() {
        UnifiedLiveConfig.OssConfig config = unifiedLiveConfig.getOss();

        if (config.getAccessKey() == null || config.getAccessKey().trim().isEmpty()) {
            log.warn("OSS AccessKey为空，OSS功能可能无法正常使用");
        }

        if (config.getSecretKey() == null || config.getSecretKey().trim().isEmpty()) {
            log.warn("OSS SecretKey为空，OSS功能可能无法正常使用");
        }

        if (config.getEndpoint() == null || config.getEndpoint().trim().isEmpty()) {
            log.warn("OSS Endpoint为空，OSS功能可能无法正常使用");
        }

        if (config.getBucketName() == null || config.getBucketName().trim().isEmpty()) {
            log.warn("OSS BucketName为空，OSS功能可能无法正常使用");
        }

        log.info("OSS配置验证完成");
    }

    /**
     * 验证阿里云配置
     */
    private void validateAliyunConfig() {
        UnifiedLiveConfig.AliyunConfig config = unifiedLiveConfig.getAliyun();

        if (config.getAccessKeyId() == null || config.getAccessKeyId().trim().isEmpty()) {
            log.warn("阿里云AccessKeyId为空，阿里云服务可能无法正常使用");
        }

        if (config.getAccessKeySecret() == null || config.getAccessKeySecret().trim().isEmpty()) {
            log.warn("阿里云AccessKeySecret为空，阿里云服务可能无法正常使用");
        }

        log.info("阿里云配置验证完成");
    }

    /**
     * 验证视频处理配置
     */
    private void validateVideoConfig() {
        UnifiedLiveConfig.VideoConfig config = unifiedLiveConfig.getVideo();

        if (config.getMaxFileSize() <= 0) {
            throw new IllegalArgumentException("最大文件大小必须大于0");
        }

        if (config.getSupportedFormats() == null || config.getSupportedFormats().length == 0) {
            throw new IllegalArgumentException("支持的视频格式不能为空");
        }

        if (config.getMaxRetryCount() < 0) {
            throw new IllegalArgumentException("最大重试次数不能小于0");
        }

        if (config.getRetryInterval() <= 0) {
            throw new IllegalArgumentException("重试间隔必须大于0");
        }

        if (config.getTranscodeTimeout() <= 0) {
            throw new IllegalArgumentException("转码超时时间必须大于0");
        }

        if (config.getTranscodeQueueSize() <= 0) {
            throw new IllegalArgumentException("转码任务队列大小必须大于0");
        }

        if (config.getTranscodeThreadPoolSize() <= 0) {
            throw new IllegalArgumentException("转码线程池大小必须大于0");
        }

        log.info("视频处理配置验证通过");
    }

    /**
     * 打印配置摘要
     */
    private void printConfigurationSummary() {
        log.info("=== 直播服务配置摘要 ===");
        log.info("推流域名: {}", unifiedLiveConfig.getStream().getPushDomain());
        log.info("播放域名: {}", unifiedLiveConfig.getStream().getPlayDomain());
        log.info("应用名称: {}", unifiedLiveConfig.getStream().getAppName());
        log.info("播放URL有效期: {} 秒", unifiedLiveConfig.getStream().getPlayUrlExpireSeconds());
        log.info("任务调度启用: {}", unifiedLiveConfig.getTask().isEnabled());
        log.info("状态检查间隔: {} 秒", unifiedLiveConfig.getTask().getStatusCheckInterval());
        log.info("缓存启用: {}", unifiedLiveConfig.getCache().isEnabled());
        log.info("最大文件大小: {} MB", unifiedLiveConfig.getVideo().getMaxFileSize());
        log.info("支持的视频格式: {}", String.join(", ", unifiedLiveConfig.getVideo().getSupportedFormats()));
        log.info("异步转码: {}", unifiedLiveConfig.getVideo().isAsyncTranscode());
        log.info("========================");
    }

    /**
     * 检查配置是否已更改
     */
    public boolean isConfigChanged(String configKey, Object newValue) {
        Object cachedValue = configCache.get(configKey);
        return cachedValue == null || !cachedValue.equals(newValue);
    }

    /**
     * 更新配置缓存
     */
    public void updateConfigCache(String configKey, Object newValue) {
        configCache.put(configKey, newValue);
        log.info("配置缓存已更新: {}", configKey);
    }

    /**
     * 获取所有配置
     */
    public Map<String, Object> getAllConfigs() {
        return new HashMap<>(configCache);
    }
}
