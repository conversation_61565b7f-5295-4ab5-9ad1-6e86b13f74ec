package com.ydwl.live.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ydwl.common.core.domain.R;
import com.ydwl.live.config.ConfigManager;
import com.ydwl.live.domain.Live;
import com.ydwl.live.domain.LiveStream;
import com.ydwl.live.domain.bo.LiveBo;
import com.ydwl.live.domain.vo.GetStreamVo;
import com.ydwl.live.domain.vo.LiveVo;
import com.ydwl.live.event.LiveCreatedEvent;
import com.ydwl.live.event.LiveEventPublisher;
import com.ydwl.live.event.LiveStreamRefreshEvent;
import com.ydwl.live.mapper.LiveMapper;
import com.ydwl.live.mapper.LiveStreamMapper;
import com.ydwl.live.service.ILiveDataService;
import com.ydwl.live.service.ILiveService;
import com.ydwl.live.service.ILiveStreamService;
import com.ydwl.live.service.model.LiveStreamInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 直播数据服务
 * 整合直播信息、状态和推流信息的服务
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@Slf4j
@Service
public class LiveDataService implements ILiveDataService {

    private final LiveMapper liveMapper;
    private final LiveStreamMapper liveStreamMapper;
    private final LiveStateManager stateManager;
    private final ILiveService liveService;
    private final ILiveStreamService liveStreamService;
    private final ConfigManager configManager;
    private final LiveEventPublisher eventPublisher;

    /**
     * 获取直播详情，包含推流和播放信息
     *
     * @param liveId 直播ID
     * @return 直播详情数据
     */
    @Override
    public Map<String, Object> getLiveDetail(Long liveId) {
        if (liveId == null) {
            return null;
        }

        Map<String, Object> result = new HashMap<>();

        try {
            // 获取直播基本信息
            LiveVo liveInfo = liveService.queryById(liveId);
            if (liveInfo == null) {
                log.warn("直播不存在：liveId={}", liveId);
                return null;
            }
            result.put("liveInfo", liveInfo);

            // 获取推流和播放信息（使用新的共享数据模型）
            LiveStreamInfo streamInfo = liveStreamService.getStreamInfoByLiveId(liveId);
            if (streamInfo != null) {
                result.put("streamInfo", streamInfo);
                result.put("playUrls", streamInfo.getPlayUrls());
            }

            // 获取直播状态
            LiveStateManager.LiveStatus status = stateManager.checkLiveStatus(liveId);
            result.put("liveStatus", status.getValue());
            result.put("liveStatusText", status.name());

            return result;
        } catch (Exception e) {
            log.error("获取直播详情异常：liveId={}", liveId, e);
            return null;
        }
    }

    /**
     * 创建直播并自动生成推流信息
     *
     * @param bo 直播信息
     * @return 创建结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Map<String, Object>> createLiveWithStream(LiveBo bo) {
        try {
            // 1. 创建直播基本信息
            boolean success = liveService.insertByBo(bo);
            if (!success) {
                log.error("创建直播基本信息失败");
                return R.fail("创建直播失败");
            }

            Long liveId = bo.getId();
            if (liveId == null) {
                log.error("获取直播ID失败");
                return R.fail("创建直播失败：无法获取直播ID");
            }
            
            // 2. 发布直播创建事件，由事件监听器处理后续逻辑
            eventPublisher.publish(new LiveCreatedEvent(liveId, bo));
            
            // 3. 创建推流信息（直接调用服务方法，不再通过事件获取结果）
            LiveStreamInfo streamInfo = liveStreamService.createStreamForLive(liveId, bo.getTitle());
            
            // 4. 返回创建结果
            Map<String, Object> result = new HashMap<>();
            result.put("liveId", liveId);
            result.put("streamId", streamInfo.getId());
            result.put("pushUrl", streamInfo.getPushUrl());
            result.put("playUrls", streamInfo.getPlayUrls());

            return R.ok(result);
        } catch (Exception e) {
            log.error("创建直播并生成推流信息异常", e);
            throw new RuntimeException("创建直播失败：" + e.getMessage(), e);
        }
    }

    /**
     * 刷新直播推流地址
     *
     * @param liveId 直播ID
     * @return 更新后的推流信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Map<String, Object>> refreshLiveStream(Long liveId) {
        try {
            // 检查直播是否存在
            Live live = liveMapper.selectById(liveId);
            if (live == null) {
                return R.fail("直播不存在");
            }

            // 发布推流刷新事件
            eventPublisher.publish(new LiveStreamRefreshEvent(liveId));
            
            // 直接获取刷新后的结果
            LiveStreamInfo streamInfo = liveStreamService.refreshStreamInfo(liveId);
            if (streamInfo == null) {
                return R.fail("刷新推流信息失败");
            }

            // 返回更新结果
            Map<String, Object> result = new HashMap<>();
            result.put("liveId", liveId);
            result.put("streamId", streamInfo.getId());
            result.put("pushUrl", streamInfo.getPushUrl());
            result.put("playUrls", streamInfo.getPlayUrls());

            return R.ok(result);
        } catch (Exception e) {
            log.error("刷新直播推流信息异常：liveId={}", liveId, e);
            throw new RuntimeException("刷新推流信息失败：" + e.getMessage(), e);
        }
    }

    /**
     * 更新直播状态
     *
     * @param liveId 直播ID
     * @param status 目标状态
     * @return 更新结果
     */
    @Override
    public R<Void> updateLiveStatus(Long liveId, LiveStateManager.LiveStatus status) {
        try {
            boolean success = stateManager.updateLiveStatus(liveId, status);
            return success ? R.ok() : R.fail("更新直播状态失败");
        } catch (Exception e) {
            log.error("更新直播状态异常：liveId={}, status={}", liveId, status, e);
            return R.fail("更新直播状态失败：" + e.getMessage());
        }
    }
    
    /**
     * 处理直播创建事件
     * 
     * @param event 直播创建事件
     */
    @EventListener
    public void handleLiveCreatedEvent(LiveCreatedEvent event) {
        log.info("处理直播创建事件：liveId={}", event.getLiveId());
        // 这里可以添加额外的业务逻辑，如通知、日志记录等
    }
    
    /**
     * 处理推流刷新事件
     * 
     * @param event 推流刷新事件
     */
    @EventListener
    public void handleLiveStreamRefreshEvent(LiveStreamRefreshEvent event) {
        log.info("处理推流刷新事件：liveId={}", event.getLiveId());
        // 这里可以添加额外的业务逻辑，如通知、日志记录等
    }
} 