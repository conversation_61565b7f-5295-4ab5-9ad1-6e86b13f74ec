package com.ydwl.live.service.stream;

import com.ydwl.live.domain.LiveStream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 推流告警服务
 * 处理推流相关的告警通知
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StreamAlertService {

    /**
     * 发送推流恢复告警
     */
    public void sendStreamRecoveryAlert(LiveStream stream, StreamMonitorService.StreamHealthStatus status) {
        log.info("推流恢复告警 - StreamId: {}, LiveId: {}, 状态: {}", 
                stream.getId(), stream.getLiveId(), status.getMessage());
        
        // 这里可以集成实际的告警系统
        // 比如：发送邮件、短信、钉钉、企业微信等
        sendAlert("推流恢复", 
                String.format("推流已恢复正常 - StreamId: %d, LiveId: %d, 详情: %s", 
                        stream.getId(), stream.getLiveId(), status.getMessage()),
                AlertLevel.INFO);
    }

    /**
     * 发送推流不稳定告警
     */
    public void sendStreamUnstableAlert(LiveStream stream, StreamMonitorService.StreamHealthStatus status) {
        log.warn("推流不稳定告警 - StreamId: {}, LiveId: {}, 原因: {}", 
                stream.getId(), stream.getLiveId(), status.getMessage());
        
        sendAlert("推流不稳定", 
                String.format("推流状态不稳定 - StreamId: %d, LiveId: %d, 原因: %s", 
                        stream.getId(), stream.getLiveId(), status.getMessage()),
                AlertLevel.WARNING);
    }

    /**
     * 发送推流断开告警
     */
    public void sendStreamDisconnectedAlert(LiveStream stream, StreamMonitorService.StreamHealthStatus status) {
        log.warn("推流断开告警 - StreamId: {}, LiveId: {}", 
                stream.getId(), stream.getLiveId());
        
        sendAlert("推流断开", 
                String.format("推流已断开 - StreamId: %d, LiveId: %d, 详情: %s", 
                        stream.getId(), stream.getLiveId(), status.getMessage()),
                AlertLevel.WARNING);
    }

    /**
     * 发送推流错误告警
     */
    public void sendStreamErrorAlert(LiveStream stream, StreamMonitorService.StreamHealthStatus status) {
        log.error("推流错误告警 - StreamId: {}, LiveId: {}, 错误: {}", 
                stream.getId(), stream.getLiveId(), status.getMessage());
        
        sendAlert("推流错误", 
                String.format("推流发生错误 - StreamId: %d, LiveId: %d, 错误: %s", 
                        stream.getId(), stream.getLiveId(), status.getMessage()),
                AlertLevel.ERROR);
    }

    /**
     * 发送自动结束直播告警
     */
    public void sendAutoEndLiveAlert(LiveStream stream, String reason) {
        log.warn("自动结束直播告警 - StreamId: {}, LiveId: {}, 原因: {}", 
                stream.getId(), stream.getLiveId(), reason);
        
        sendAlert("自动结束直播", 
                String.format("直播已自动结束 - StreamId: %d, LiveId: %d, 原因: %s", 
                        stream.getId(), stream.getLiveId(), reason),
                AlertLevel.WARNING);
    }

    /**
     * 发送告警
     */
    private void sendAlert(String title, String message, AlertLevel level) {
        // 这里实现具体的告警发送逻辑
        // 可以根据告警级别选择不同的通知方式
        
        switch (level) {
            case INFO:
                log.info("告警通知 [{}] - {}: {}", level, title, message);
                // 可以发送到日志系统或监控平台
                break;
            case WARNING:
                log.warn("告警通知 [{}] - {}: {}", level, title, message);
                // 可以发送邮件或即时消息
                sendEmailAlert(title, message);
                break;
            case ERROR:
                log.error("告警通知 [{}] - {}: {}", level, title, message);
                // 可以发送短信、电话或紧急通知
                sendEmailAlert(title, message);
                sendSmsAlert(title, message);
                break;
        }
    }

    /**
     * 发送邮件告警
     */
    private void sendEmailAlert(String title, String message) {
        // 实现邮件发送逻辑
        log.debug("发送邮件告警: {} - {}", title, message);
        // 这里可以集成邮件服务
    }

    /**
     * 发送短信告警
     */
    private void sendSmsAlert(String title, String message) {
        // 实现短信发送逻辑
        log.debug("发送短信告警: {} - {}", title, message);
        // 这里可以集成短信服务
    }

    /**
     * 告警级别
     */
    public enum AlertLevel {
        INFO,    // 信息
        WARNING, // 警告
        ERROR    // 错误
    }
}
