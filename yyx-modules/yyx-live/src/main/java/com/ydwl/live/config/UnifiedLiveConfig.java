package com.ydwl.live.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 统一的直播配置类
 * 整合所有直播相关配置，避免配置分散
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "ydwl.live")
public class UnifiedLiveConfig {

    /**
     * 推流配置
     */
    private StreamConfig stream = new StreamConfig();

    /**
     * 任务配置
     */
    private TaskConfig task = new TaskConfig();

    /**
     * 认证配置
     */
    private AuthConfig auth = new AuthConfig();

    /**
     * 视频处理配置
     */
    private VideoConfig video = new VideoConfig();

    /**
     * 缓存配置
     */
    private CacheConfig cache = new CacheConfig();

    /**
     * 推流配置
     */
    @Data
    public static class StreamConfig {
        /**
         * 推流域名
         */
        private String pushDomain = "push.live.ycyyx.com";

        /**
         * 播放域名
         */
        private String playDomain = "play.live.ycyyx.com";

        /**
         * 应用名称
         */
        private String appName = "live";

        /**
         * 推流鉴权密钥
         */
        private String pushSecretKey = "2p5n2R8aA6tTQtu2";

        /**
         * 播放鉴权密钥
         */
        private String playSecretKey = "LznJujBX81mOJM7E";

        /**
         * 播放URL有效期（秒）
         */
        private long playUrlExpireSeconds = 86400;

        /**
         * 推流超时时间（秒）
         */
        private int pushTimeout = 300;
    }

    /**
     * 任务配置
     */
    @Data
    public static class TaskConfig {
        /**
         * 是否启用定时任务
         */
        private boolean enabled = true;

        /**
         * 直播状态检查间隔（秒）
         */
        private int statusCheckInterval = 60;

        /**
         * 录制检查间隔（秒）
         */
        private int recordCheckInterval = 120;
    }

    /**
     * 认证配置
     */
    @Data
    public static class AuthConfig {
        /**
         * token过期时间(单位:秒)
         */
        private long tokenTimeout = 86400;

        /**
         * 是否启用访问控制
         */
        private boolean accessControlEnabled = true;
    }

    /**
     * 视频处理配置
     */
    @Data
    public static class VideoConfig {
        /**
         * 最大文件大小（MB）
         */
        private long maxFileSize = 2048;

        /**
         * 支持的视频格式
         */
        private String[] supportedFormats = {"mp4", "flv", "m3u8", "mov"};

        /**
         * 转码重试次数
         */
        private int maxRetryCount = 3;

        /**
         * 重试间隔（秒）
         */
        private int retryInterval = 300;

        /**
         * 转码超时时间（秒）
         */
        private int transcodeTimeout = 7200;

        /**
         * 是否异步转码
         */
        private boolean asyncTranscode = true;

        /**
         * 转码任务队列大小
         */
        private int transcodeQueueSize = 100;

        /**
         * 转码线程池大小
         */
        private int transcodeThreadPoolSize = 5;
    }

    /**
     * 缓存配置
     */
    @Data
    public static class CacheConfig {
        /**
         * 直播信息缓存时间（秒）
         */
        private long liveInfoCacheTime = 300;

        /**
         * 推流信息缓存时间（秒）
         */
        private long streamInfoCacheTime = 600;

        /**
         * 用户信息缓存时间（秒）
         */
        private long userInfoCacheTime = 1800;

        /**
         * 是否启用缓存
         */
        private boolean enabled = true;
    }
}
