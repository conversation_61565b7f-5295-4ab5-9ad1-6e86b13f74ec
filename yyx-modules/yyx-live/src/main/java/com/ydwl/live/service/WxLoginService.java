package com.ydwl.live.service;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import cn.dev33.satoken.stp.SaLoginModel;
import cn.dev33.satoken.stp.StpUtil;
import com.ydwl.common.core.domain.model.LoginUser;
import com.ydwl.common.core.exception.ServiceException;
import com.ydwl.common.core.utils.MapstructUtils;
import com.ydwl.common.core.utils.StringUtils;
import com.ydwl.live.config.ConfigManager;
import com.ydwl.live.domain.LiveLoginUser;
import com.ydwl.live.domain.LiveUser;
import com.ydwl.live.domain.bo.WxLoginBo;
import com.ydwl.live.domain.vo.LiveLoginVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WxLoginService {

    private final WxMaService wxMaService;
    private final ILiveUserService liveUserService;
    private final ConfigManager configManager;

    public LiveLoginVo login(WxLoginBo wxLoginBo) {
        try {
            WxMaJscode2SessionResult session = wxMaService.getUserService().getSessionInfo(wxLoginBo.getCode());
            String openid = session.getOpenid();
            LiveUser liveUser = liveUserService.getUserByOpenid(openid);

            if (liveUser == null) {
                // 用户不存在，执行注册
                LiveUser newUser = new LiveUser();
                newUser.setOpenid(openid);
                newUser.setNickname(wxLoginBo.getNickname());
                newUser.setAvatarUrl(wxLoginBo.getAvatarUrl());
                liveUser = liveUserService.registerUser(newUser);
            } else {
                // 用户存在，更新基本信息
                boolean needUpdate = false;
                if (StringUtils.isNotBlank(wxLoginBo.getNickname()) && !wxLoginBo.getNickname().equals(liveUser.getNickname())) {
                    liveUser.setNickname(wxLoginBo.getNickname());
                    needUpdate = true;
                }
                if (StringUtils.isNotBlank(wxLoginBo.getAvatarUrl()) && !wxLoginBo.getAvatarUrl().equals(liveUser.getAvatarUrl())) {
                    liveUser.setAvatarUrl(wxLoginBo.getAvatarUrl());
                    needUpdate = true;
                }
                if (needUpdate) {
                    liveUserService.updateUserInfo(liveUser);
                }
            }


            // 构建登录用户
            LiveLoginUser loginUser = MapstructUtils.convert(liveUser, LiveLoginUser.class);
            loginUser.setDeviceType(wxLoginBo.getDeviceType());

            // 生成token
            SaLoginModel model = new SaLoginModel();
            model.setDevice(wxLoginBo.getDeviceType());
            model.setTimeout(configManager.getAuthConfig().getTokenTimeout());
            // 登录
            StpUtil.login(loginUser.getId(), model);
            // token写入session
            StpUtil.getTokenSession().set("loginUser", loginUser);
            StpUtil.getTokenSession().set("sessionKey", session.getSessionKey());


            // 构建响应
            LiveLoginVo loginVo = new LiveLoginVo();
            loginVo.setAccessToken(StpUtil.getTokenValue());
            loginVo.setExpireIn(StpUtil.getTokenTimeout());
            loginVo.setOpenid(openid);
            loginVo.setClientId(wxMaService.getWxMaConfig().getAppid());

            return loginVo;

        } catch (WxErrorException e) {
            log.error("微信登录失败", e);
            throw new ServiceException("微信登录失败: " + e.getMessage());
        }
    }

    public LiveUser getPhoneNumber(WxLoginBo wxLoginBo) {
        long loginId = StpUtil.getLoginIdAsLong();
        String sessionKey = StpUtil.getTokenSession().getString("sessionKey");
        if (sessionKey == null) {
            throw new ServiceException("会话已过期，请重新登录");
        }
        try {
            // 解密手机号
            WxMaPhoneNumberInfo phoneNoInfo = wxMaService.getUserService().getPhoneNoInfo(sessionKey, wxLoginBo.getEncryptedData(), wxLoginBo.getIv());
            // 更新用户信息
            LiveUser user = new LiveUser();
            user.setId(loginId);
            user.setPhone(phoneNoInfo.getPhoneNumber());
            return liveUserService.updateUserInfo(user);
        } catch (Exception e) {
            log.error("解密手机号失败", e);
            throw new ServiceException("解密手机号失败");
        }
    }
}
