package com.ydwl.live.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * 配置迁移助手
 * 帮助从旧的分散配置迁移到新的统一配置
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(name = "ydwl.live.config.migration.enabled", havingValue = "true", matchIfMissing = false)
public class ConfigMigrationHelper {

    private final UnifiedLiveConfig unifiedLiveConfig;

    // 注入旧的配置类（如果存在）
    private final LiveStreamConfig oldStreamConfig;
    private final VideoProcessConfig oldVideoConfig;
    private final OssConfigProperties oldOssConfig;
    private final LiveStreamConfigProperties oldAliyunConfig;

    @PostConstruct
    public void migrateConfigurations() {
        log.info("开始配置迁移...");

        try {
            migrateStreamConfig();
            migrateVideoConfig();
            migrateOssConfig();
            migrateAliyunConfig();
            
            log.info("配置迁移完成");
            printMigrationSummary();
        } catch (Exception e) {
            log.error("配置迁移失败", e);
        }
    }

    /**
     * 迁移推流配置
     */
    private void migrateStreamConfig() {
        if (oldStreamConfig != null) {
            UnifiedLiveConfig.StreamConfig newConfig = unifiedLiveConfig.getStream();
            
            // 只有当新配置为默认值时才迁移
            if (isDefaultStreamConfig(newConfig)) {
                newConfig.setPushDomain(oldStreamConfig.getPushDomain());
                newConfig.setPlayDomain(oldStreamConfig.getPlayDomain());
                newConfig.setAppName(oldStreamConfig.getAppName());
                newConfig.setPushSecretKey(oldStreamConfig.getPushSecretKey());
                newConfig.setPlaySecretKey(oldStreamConfig.getPlaySecretKey());
                newConfig.setPlayUrlExpireSeconds(oldStreamConfig.getPlayUrlExpireSeconds());
                
                log.info("推流配置迁移完成");
            } else {
                log.info("推流配置已存在，跳过迁移");
            }
        }
    }

    /**
     * 迁移视频处理配置
     */
    private void migrateVideoConfig() {
        if (oldVideoConfig != null) {
            UnifiedLiveConfig.VideoConfig newConfig = unifiedLiveConfig.getVideo();
            
            if (isDefaultVideoConfig(newConfig)) {
                newConfig.setMaxFileSize(oldVideoConfig.getMaxFileSize());
                newConfig.setSupportedFormats(oldVideoConfig.getSupportedFormats());
                newConfig.setMaxRetryCount(oldVideoConfig.getMaxRetryCount());
                newConfig.setRetryInterval(oldVideoConfig.getRetryInterval());
                newConfig.setTranscodeTimeout(oldVideoConfig.getTranscodeTimeout());
                newConfig.setAsyncTranscode(oldVideoConfig.isAsyncTranscode());
                newConfig.setTranscodeQueueSize(oldVideoConfig.getTranscodeQueueSize());
                newConfig.setTranscodeThreadPoolSize(oldVideoConfig.getTranscodeThreadPoolSize());
                
                log.info("视频处理配置迁移完成");
            } else {
                log.info("视频处理配置已存在，跳过迁移");
            }
        }
    }

    /**
     * 迁移OSS配置
     */
    private void migrateOssConfig() {
        if (oldOssConfig != null) {
            UnifiedLiveConfig.OssConfig newConfig = unifiedLiveConfig.getOss();
            
            if (isDefaultOssConfig(newConfig)) {
                newConfig.setAccessKey(oldOssConfig.getAccessKey());
                newConfig.setSecretKey(oldOssConfig.getSecretKey());
                newConfig.setEndpoint(oldOssConfig.getEndpoint());
                newConfig.setBucketName(oldOssConfig.getBucketName());
                newConfig.setNotifyTopic(oldOssConfig.getNotifyTopic());
                newConfig.setCallbackUrl(oldOssConfig.getCallbackUrl());
                newConfig.setMaxFileSize(oldOssConfig.getMaxFileSize());
                
                log.info("OSS配置迁移完成");
            } else {
                log.info("OSS配置已存在，跳过迁移");
            }
        }
    }

    /**
     * 迁移阿里云配置
     */
    private void migrateAliyunConfig() {
        if (oldAliyunConfig != null) {
            UnifiedLiveConfig.AliyunConfig newConfig = unifiedLiveConfig.getAliyun();
            
            if (isDefaultAliyunConfig(newConfig)) {
                newConfig.setAccessKeyId(oldAliyunConfig.getAccessKeyId());
                newConfig.setAccessKeySecret(oldAliyunConfig.getAccessKeySecret());
                newConfig.setRegionId(oldAliyunConfig.getRegionId());
                newConfig.setDomainName(oldAliyunConfig.getDomainName());
                newConfig.setOssEndpoint(oldAliyunConfig.getOssEndpoint());
                newConfig.setOssBucket(oldAliyunConfig.getOssBucket());
                
                log.info("阿里云配置迁移完成");
            } else {
                log.info("阿里云配置已存在，跳过迁移");
            }
        }
    }

    /**
     * 检查是否为默认推流配置
     */
    private boolean isDefaultStreamConfig(UnifiedLiveConfig.StreamConfig config) {
        return "push.live.ycyyx.com".equals(config.getPushDomain()) &&
               "play.live.ycyyx.com".equals(config.getPlayDomain()) &&
               "live".equals(config.getAppName());
    }

    /**
     * 检查是否为默认视频配置
     */
    private boolean isDefaultVideoConfig(UnifiedLiveConfig.VideoConfig config) {
        return config.getMaxFileSize() == 2048 &&
               config.getMaxRetryCount() == 3 &&
               config.getRetryInterval() == 300;
    }

    /**
     * 检查是否为默认OSS配置
     */
    private boolean isDefaultOssConfig(UnifiedLiveConfig.OssConfig config) {
        return config.getAccessKey() == null || config.getAccessKey().trim().isEmpty();
    }

    /**
     * 检查是否为默认阿里云配置
     */
    private boolean isDefaultAliyunConfig(UnifiedLiveConfig.AliyunConfig config) {
        return config.getAccessKeyId() == null || config.getAccessKeyId().trim().isEmpty();
    }

    /**
     * 打印迁移摘要
     */
    private void printMigrationSummary() {
        log.info("=== 配置迁移摘要 ===");
        log.info("推流配置: {}", oldStreamConfig != null ? "已迁移" : "无需迁移");
        log.info("视频配置: {}", oldVideoConfig != null ? "已迁移" : "无需迁移");
        log.info("OSS配置: {}", oldOssConfig != null ? "已迁移" : "无需迁移");
        log.info("阿里云配置: {}", oldAliyunConfig != null ? "已迁移" : "无需迁移");
        log.info("==================");
    }

    /**
     * 生成迁移指南
     */
    public String generateMigrationGuide() {
        StringBuilder guide = new StringBuilder();
        guide.append("# 配置迁移指南\n\n");
        
        guide.append("## 旧配置 -> 新配置映射\n\n");
        
        guide.append("### 推流配置\n");
        guide.append("```yaml\n");
        guide.append("# 旧配置\n");
        guide.append("ydwl.live.stream:\n");
        guide.append("  push-domain: push.live.ycyyx.com\n");
        guide.append("  play-domain: play.live.ycyyx.com\n");
        guide.append("\n");
        guide.append("# 新配置\n");
        guide.append("ydwl.live:\n");
        guide.append("  stream:\n");
        guide.append("    push-domain: push.live.ycyyx.com\n");
        guide.append("    play-domain: play.live.ycyyx.com\n");
        guide.append("```\n\n");
        
        guide.append("### 视频处理配置\n");
        guide.append("```yaml\n");
        guide.append("# 旧配置\n");
        guide.append("video.process:\n");
        guide.append("  max-file-size: 2048\n");
        guide.append("\n");
        guide.append("# 新配置\n");
        guide.append("ydwl.live:\n");
        guide.append("  video:\n");
        guide.append("    max-file-size: 2048\n");
        guide.append("```\n\n");
        
        guide.append("### OSS配置\n");
        guide.append("```yaml\n");
        guide.append("# 旧配置\n");
        guide.append("ydwl.oss:\n");
        guide.append("  access-key: your-access-key\n");
        guide.append("\n");
        guide.append("# 新配置\n");
        guide.append("ydwl.live:\n");
        guide.append("  oss:\n");
        guide.append("    access-key: your-access-key\n");
        guide.append("```\n\n");
        
        guide.append("### 阿里云配置\n");
        guide.append("```yaml\n");
        guide.append("# 旧配置\n");
        guide.append("aliyun.live:\n");
        guide.append("  access-key-id: your-access-key-id\n");
        guide.append("\n");
        guide.append("# 新配置\n");
        guide.append("ydwl.live:\n");
        guide.append("  aliyun:\n");
        guide.append("    access-key-id: your-access-key-id\n");
        guide.append("```\n\n");
        
        guide.append("## 迁移步骤\n\n");
        guide.append("1. 启用配置迁移: `ydwl.live.config.migration.enabled=true`\n");
        guide.append("2. 重启应用，自动迁移配置\n");
        guide.append("3. 验证新配置是否正确\n");
        guide.append("4. 删除旧配置类和配置项\n");
        guide.append("5. 关闭配置迁移: `ydwl.live.config.migration.enabled=false`\n");
        
        return guide.toString();
    }
}
