package com.ydwl.live.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 配置变更事件
 * 当配置发生变更时触发此事件
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Getter
public class ConfigChangeEvent extends ApplicationEvent {

    /**
     * 配置类型
     */
    private final String configType;

    /**
     * 配置键
     */
    private final String configKey;

    /**
     * 旧值
     */
    private final Object oldValue;

    /**
     * 新值
     */
    private final Object newValue;

    /**
     * 操作用户ID
     */
    private final Long operatorId;

    public ConfigChangeEvent(Object source, String configType, String configKey, 
                           Object oldValue, Object newValue, Long operatorId) {
        super(source);
        this.configType = configType;
        this.configKey = configKey;
        this.oldValue = oldValue;
        this.newValue = newValue;
        this.operatorId = operatorId;
    }

    @Override
    public String toString() {
        return String.format("ConfigChangeEvent{configType='%s', configKey='%s', oldValue=%s, newValue=%s, operatorId=%d}",
                configType, configKey, oldValue, newValue, operatorId);
    }
}
