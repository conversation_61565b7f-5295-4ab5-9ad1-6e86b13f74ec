package com.ydwl.live.util;


import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.AsyncProcessObjectRequest;
import com.aliyun.oss.model.AsyncProcessObjectResult;
import com.ydwl.LiveTranscoding.config.GlobalConfigProperties;
import com.ydwl.live.config.ConfigManager;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.Base64;
import java.util.List;
import java.util.ArrayList;


/**
 * 阿里云OSS视频处理工具类
 * 用于处理视频截图、多帧截取等OSS异步操作
 */
@Slf4j
@Component
public class AliyunOssVideoUtil {

    @Autowired
    private ConfigManager configManager;

    private OSS ossClient;

    /**
     * 初始化OSS客户端
     */
    @PostConstruct
    public void init() {
        try {
            // 创建OSS客户端
            this.ossClient = new OSSClientBuilder().build(
                configManager.getOssConfig().getEndpoint(),
                configManager.getOssConfig().getAccessKey(),
                configManager.getOssConfig().getSecretKey()
            );
            log.info("Aliyun OSS client initialized successfully.");
        } catch (Exception e) {
            log.error("Failed to initialize Aliyun OSS client", e);
            throw new RuntimeException("Failed to initialize Aliyun OSS client", e);
        }
    }

    /**
     * 从视频中截取多帧图片，支持丰富的参数配置
     *
     * @param objectKey 源视频在OSS中的对象键
     * @param bucket 存储桶名称
     * @param outputPrefix 输出图片前缀
     * @param startTimeMs 截图开始时间（毫秒）
     * @param format 输出图片格式（jpg/png）
     * @param num 截图数量，默认不限制（截到视频结束）
     * @param intervalMs 截图间隔（毫秒）
     * @param width 输出图片宽度（可选）
     * @param height 输出图片高度（可选）
     * @param scaleType 缩放方式（crop/stretch/fill/fit）
     * @return 处理任务ID，用于后续查询
     */
    public String submitMultiFrameSnapshotJob(String objectKey, String bucket, String outputPrefix,
                                              int startTimeMs, String format, Integer num, Integer intervalMs,
                                              Integer width, Integer height, String scaleType) {
        try {
            log.info("[截图排查] 开始提交多帧截图任务，参数: objectKey={}, bucket={}, outputPrefix={}, startTimeMs={}, format={}, num={}, intervalMs={}, width={}, height={}, scaleType={}",
                     objectKey, bucket, outputPrefix, startTimeMs, format, num, intervalMs, width, height, scaleType);

            if (StringUtils.isEmpty(objectKey) || StringUtils.isEmpty(bucket) || StringUtils.isEmpty(outputPrefix)) {
                log.error("[截图排查] 提交多帧截图任务失败：参数不完整");
                return null;
            }

            if (StringUtils.isEmpty(format)) {
                format = "jpg";
                log.info("[截图排查] 使用默认格式: jpg");
            }

            if (StringUtils.isEmpty(scaleType)) {
                scaleType = "stretch";
                log.info("[截图排查] 使用默认缩放方式: stretch");
            }

            // 构建视频截图处理参数
            StringBuilder styleBuilder = new StringBuilder("video/snapshots");

            // 添加开始时间
            if (startTimeMs > 0) {
                styleBuilder.append(",ss_").append(startTimeMs);
            }

            // 添加输出格式
            styleBuilder.append(",f_").append(format);

            // 添加截图数量（如果指定）
            if (num != null && num > 0) {
                styleBuilder.append(",num_").append(num);
            }

            // 添加截图间隔（如果指定）
            if (intervalMs != null && intervalMs > 0) {
                styleBuilder.append(",inter_").append(intervalMs);
            }

            // 添加输出宽度（如果指定）
            if (width != null && width > 0) {
                styleBuilder.append(",w_").append(width);
            }

            // 添加输出高度（如果指定）
            if (height != null && height > 0) {
                styleBuilder.append(",h_").append(height);
            }

            // 添加缩放方式
            styleBuilder.append(",scaletype_").append(scaleType);

            log.info("[截图排查] 生成的样式参数: {}", styleBuilder.toString());

            // 构建输出路径 - 使用Base64编码
            String bucketEncoded = Base64.getUrlEncoder().withoutPadding().encodeToString(bucket.getBytes());
            log.info("[截图排查] 桶编码结果: {}", bucketEncoded);

            // 新逻辑: 截图将保存在与源视频相同的目录下。
            // 'outputPrefix' 参数现在被视为截图文件的基本名称前缀。
            // 调用方需要确保传入的 outputPrefix 是一个纯文件名，不包含路径分隔符。
            log.info("[截图排查] 'outputPrefix' 参数 (应为纯文件名部分) 接收到的值: {}", outputPrefix);

            String sourceFileDirectory = "";
            int lastSeparator = objectKey.lastIndexOf('/');
            if (lastSeparator != -1) {
                sourceFileDirectory = objectKey.substring(0, lastSeparator + 1); // 例如 "path/to/video/"
            }

            // outputPrefix 现在代表截图的文件名部分
            String targetSnapshotObjectKey = sourceFileDirectory + outputPrefix + "-{index}.{autoext}";

            log.info("[截图排查] 计算出的源文件目录: {}", sourceFileDirectory);
            log.info("[截图排查] 使用的截图基本文件名: {}", outputPrefix);

            String outputEncoded = Base64.getUrlEncoder().withoutPadding().encodeToString(targetSnapshotObjectKey.getBytes());
            log.info("[截图排查] 最终目标截图对象键 (同级目录): {}, 编码结果: {}", targetSnapshotObjectKey, outputEncoded);

            // 构建完整的处理指令
            String process = styleBuilder.toString() +
                             "|sys/saveas,b_" + bucketEncoded +
                             ",o_" + outputEncoded;

            // 添加通知配置（如果需要）
            if (StringUtils.isNotEmpty(configManager.getOssConfig().getCallbackUrl())) {
                String encodedNotifyTopic = Base64.getUrlEncoder().withoutPadding().encodeToString(configManager.getOssConfig().getCallbackUrl().getBytes());
                process += ",notify_topic_" + encodedNotifyTopic;
            }

            log.info("[截图排查] 提交多帧截图任务，完整处理指令：{}", process);

            // 创建异步处理请求
            AsyncProcessObjectRequest request = new AsyncProcessObjectRequest(bucket, objectKey, process);
            log.info("[截图排查] 创建异步处理请求成功，准备提交到OSS");

            // 检查对象是否存在
            try {
                boolean exists = ossClient.doesObjectExist(bucket, objectKey);
                log.info("[截图排查] 检查源视频文件是否存在: {}，桶: {}, 对象键: {}", exists, bucket, objectKey);
                if (!exists) {
                    log.error("[截图排查] 源视频文件不存在，无法截图，请检查对象键是否正确");
                    return null;
                }
            } catch (Exception e) {
                log.error("[截图排查] 检查对象存在性时发生异常: {}", e.getMessage(), e);
            }
            try {
                // 执行异步处理
                AsyncProcessObjectResult result = ossClient.asyncProcessObject(request);
                log.info("[截图排查] 提交多帧截图任务成功，TaskId: {}, RequestId: {}", result.getTaskId(), result.getRequestId());
                return result.getTaskId();
            } catch (Exception e) {
                log.error("[截图排查] OSS异步处理调用失败: {}", e.getMessage(), e);
                return null;
            }
        } catch (Exception e) {
            log.error("[截图排查] 提交多帧截图任务异常：{}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 从视频中截取第一帧作为封面图
     *
     * @param objectKey 源视频在OSS中的对象键
     * @param bucket 存储桶名称
     * @param outputPrefix 输出图片前缀
     * @param format 输出图片格式（jpg/png）
     * @param width 输出图片宽度（可选）
     * @param height 输出图片高度（可选）
     * @return 处理任务ID，用于后续查询
     */
    public String submitFirstFrameSnapshotJob(String objectKey, String bucket, String outputPrefix,
                                             String format, Integer width, Integer height) {
        // 从视频开始位置截取1帧作为封面
        String taskId = submitMultiFrameSnapshotJob(
            objectKey, bucket, outputPrefix,
            0, format, 1, null, // num=1 for first frame
            width, height, "stretch"
        );

        if (taskId == null) {
            log.warn("[截图排查-预测] OSS首帧截图任务提交失败，无法生成预测URL。objectKey: {}, outputPrefix: {}", objectKey, outputPrefix);
            return null; // Submission failed
        }

        // 如果任务提交成功，则预测输出对象键和URL
        // 根据 submitMultiFrameSnapshotJob 中的逻辑:
        // targetSnapshotObjectKey = sourceFileDirectory + snapshotBaseName + "-{index}.{autoext}";
        // snapshotBaseName 就是这里的 outputPrefix
        // {index} 将是 "0" 因为 num=1 (首个也是唯一一个截图)
        // {autoext} 将是 format (例如 "jpg")

        String sourceFileDirectory = "";
        int lastSeparator = objectKey.lastIndexOf('/');
        if (lastSeparator != -1) {
            sourceFileDirectory = objectKey.substring(0, lastSeparator + 1); // 例如 "path/to/video/"
        }

        String predictedObjectKey = sourceFileDirectory + outputPrefix + "-0." + format;

        // 从配置属性获取OSS Endpoint
        String predictedUrl = "https://" + bucket + "." + configManager.getOssConfig().getEndpoint() + "/" + predictedObjectKey;

        log.info("[截图排查-预测] 成功提交首帧截图任务 (taskId: {})。预测截图对象键: {}, 预测URL: {}",
                 taskId, predictedObjectKey, predictedUrl);
        return predictedUrl; // 返回预测的URL
    }

    /**
     * 从视频中每隔N秒截取一帧
     *
     * @param objectKey 源视频在OSS中的对象键
     * @param bucket 存储桶名称
     * @param outputPrefix 输出图片前缀
     * @param intervalSeconds 截图间隔（秒）
     * @param format 输出图片格式（jpg/png）
     * @param maxFrames 最大截图数量（可选）
     * @return 处理任务ID，用于后续查询
     */
    public String submitIntervalFrameSnapshotJob(String objectKey, String bucket, String outputPrefix,
                                               int intervalSeconds, String format, Integer maxFrames) {
        // 每隔N秒截取一帧
        return submitMultiFrameSnapshotJob(
            objectKey, bucket, outputPrefix,
            0, format, maxFrames, intervalSeconds * 1000,
            null, null, "stretch"
        );
    }

    /**
     * 从视频中提取关键帧作为截图
     * 使用场景：获取视频中内容变化显著的帧
     * @param objectKey 源视频在OSS中的对象键
     * @param bucket 存储桶名称
     * @param outputPrefix 输出图片前缀
     * @param format 输出图片格式（jpg/png）
     * @param maxFrames 最大截图数量（可选）
     * @param width 输出图片宽度（可选）
     * @param height 输出图片高度（可选）
     * @param scaleType 缩放方式（crop/stretch/fill/fit）
     * @return 处理任务ID，用于后续查询
     */
    public String submitKeyFrameSnapshotJob(String objectKey, String bucket, String outputPrefix,
                                           String format, Integer maxFrames, Integer width, Integer height, String scaleType) {
        // 关键帧提取，间隔设为0表示提取I帧
        return submitMultiFrameSnapshotJob(
            objectKey, bucket, outputPrefix,
            0, format, maxFrames, 0,
            width, height, scaleType != null ? scaleType : "stretch"
        );
    }

    /**
     * 从视频中按百分比位置截取帧
     * 使用场景：获取视频不同进度的预览图
     *
     * @param objectKey 源视频在OSS中的对象键
     * @param bucket 存储桶名称
     * @param outputPrefix 输出图片前缀
     * @param percentages 百分比位置数组，例如[0, 25, 50, 75, 100]表示在视频开始、四分之一、中点、四分之三和结尾处截图
     * @param format 输出图片格式（jpg/png）
     * @param width 输出图片宽度（可选）
     * @param height 输出图片高度（可选）
     * @return 处理任务ID数组，按百分比位置顺序
     */
    public List<String> submitPercentageSnapshotJobs(String objectKey, String bucket, String outputPrefix,
                                                  int[] percentages, String format, Integer width, Integer height) {
        if (percentages == null || percentages.length == 0) {
            log.error("[截图排查] 提交百分比截图任务失败：未指定百分比位置");
            return null;
        }

        try {
            // 先获取视频时长信息（此处假设有获取视频信息的方法，实际项目中需要实现）
            // long durationMs = getVideoDuration(bucket, objectKey);
            // 由于这里无法实际获取视频时长，暂时跳过按百分比计算时间点的步骤

            List<String> taskIds = new ArrayList<>();
            for (int i = 0; i < percentages.length; i++) {
                int percentage = percentages[i];
                if (percentage < 0 || percentage > 100) {
                    log.warn("[截图排查] 百分比值超出范围(0-100)，跳过: {}", percentage);
                    continue;
                }

                // 实际应用中，应该根据视频时长计算时间点
                // long timeMs = durationMs * percentage / 100;

                // 由于无法获取实际时长，这里仅作为示例，用序号区分不同百分比的截图
                String percentOutputPrefix = outputPrefix + "-p" + percentage;

                // 为每个百分比位置提交单帧截图任务
                String taskId = submitMultiFrameSnapshotJob(
                    objectKey, bucket, percentOutputPrefix,
                    0, format, 1, null,
                    width, height, "stretch"
                );

                if (taskId != null) {
                    taskIds.add(taskId);
                    log.info("[截图排查] 提交百分比位置{}%的截图任务成功，TaskId: {}", percentage, taskId);
                }
            }

            return taskIds;
        } catch (Exception e) {
            log.error("[截图排查] 提交百分比截图任务异常: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 关闭OSS客户端
     */
    public void shutdown() {
        if (ossClient != null) {
            ossClient.shutdown();
        }
    }
}
