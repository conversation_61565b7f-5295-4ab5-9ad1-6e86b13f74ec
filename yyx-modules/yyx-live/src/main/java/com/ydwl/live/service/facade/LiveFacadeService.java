package com.ydwl.live.service.facade;

import com.ydwl.common.core.domain.R;
import com.ydwl.common.mybatis.core.page.PageQuery;
import com.ydwl.common.mybatis.core.page.TableDataInfo;
import com.ydwl.live.domain.bo.LiveBo;
import com.ydwl.live.domain.vo.LiveVo;
import com.ydwl.live.service.ILiveService;
import com.ydwl.live.service.ILiveStreamService;
import com.ydwl.live.service.ILiveReplayService;
import com.ydwl.live.service.impl.LiveStateManager;
import com.ydwl.live.service.model.LiveStreamInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;
import java.util.HashMap;
import java.util.List;

/**
 * 直播门面服务
 * 提供统一的直播业务入口，整合多个服务的复杂操作
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LiveFacadeService {

    private final ILiveService liveService;
    private final ILiveStreamService liveStreamService;
    private final ILiveReplayService liveReplayService;
    private final LiveStateManager stateManager;

    /**
     * 创建完整的直播（包含推流信息）
     *
     * @param bo 直播信息
     * @return 创建结果，包含直播信息和推流信息
     */
    @Transactional(rollbackFor = Exception.class)
    public R<Map<String, Object>> createCompleteLive(LiveBo bo) {
        try {
            log.info("开始创建完整直播，标题: {}", bo.getTitle());

            // 1. 创建直播基础信息
            Boolean liveCreated = liveService.insertByBo(bo);
            if (!liveCreated) {
                return R.fail("创建直播失败");
            }

            // 2. 为直播创建推流信息
            LiveStreamInfo streamInfo = liveStreamService.createStreamForLive(bo.getId(), bo.getTitle());
            if (streamInfo == null) {
                throw new RuntimeException("创建推流信息失败");
            }

            // 3. 初始化直播状态
            stateManager.initializeLiveState(bo.getId());

            // 4. 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("liveInfo", liveService.queryById(bo.getId()));
            result.put("streamInfo", streamInfo);

            log.info("完整直播创建成功，直播ID: {}, 推流ID: {}", bo.getId(), streamInfo.getId());
            return R.ok(result);

        } catch (Exception e) {
            log.error("创建完整直播失败，标题: {}", bo.getTitle(), e);
            return R.fail("创建直播失败: " + e.getMessage());
        }
    }

    /**
     * 获取直播完整信息
     *
     * @param liveId 直播ID
     * @return 包含直播、推流、回放等完整信息
     */
    public R<Map<String, Object>> getCompleteLiveInfo(Long liveId) {
        try {
            log.info("获取完整直播信息，直播ID: {}", liveId);

            // 1. 获取直播基础信息
            LiveVo liveInfo = liveService.queryById(liveId);
            if (liveInfo == null) {
                return R.fail("直播不存在");
            }

            // 2. 获取推流信息
            LiveStreamInfo streamInfo = liveStreamService.getStreamInfoByLiveId(liveId);

            // 3. 获取回放信息
            List<?> replayList = liveReplayService.queryListByLiveId(liveId);

            // 4. 获取直播状态
            LiveStateManager.LiveStatus currentStatus = stateManager.getCurrentStatus(liveId);

            // 5. 构建完整信息
            Map<String, Object> result = new HashMap<>();
            result.put("liveInfo", liveInfo);
            result.put("streamInfo", streamInfo);
            result.put("replayList", replayList);
            result.put("currentStatus", currentStatus);

            return R.ok(result);

        } catch (Exception e) {
            log.error("获取完整直播信息失败，直播ID: {}", liveId, e);
            return R.fail("获取直播信息失败: " + e.getMessage());
        }
    }

    /**
     * 开始直播
     *
     * @param liveId 直播ID
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    public R<Void> startLive(Long liveId) {
        try {
            log.info("开始直播，直播ID: {}", liveId);

            // 1. 验证直播状态
            LiveStateManager.LiveStatus currentStatus = stateManager.getCurrentStatus(liveId);
            if (!stateManager.canTransitionTo(currentStatus, LiveStateManager.LiveStatus.LIVE)) {
                return R.fail("当前状态不允许开始直播");
            }

            // 2. 更新直播状态
            stateManager.updateLiveStatus(liveId, LiveStateManager.LiveStatus.LIVE);

            // 3. 更新直播开始时间
            LiveBo updateBo = new LiveBo();
            updateBo.setId(liveId);
            updateBo.setActualStartTime(new java.util.Date());
            liveService.updateByBo(updateBo);

            log.info("直播开始成功，直播ID: {}", liveId);
            return R.ok();

        } catch (Exception e) {
            log.error("开始直播失败，直播ID: {}", liveId, e);
            return R.fail("开始直播失败: " + e.getMessage());
        }
    }

    /**
     * 结束直播
     *
     * @param liveId 直播ID
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    public R<Void> endLive(Long liveId) {
        try {
            log.info("结束直播，直播ID: {}", liveId);

            // 1. 验证直播状态
            LiveStateManager.LiveStatus currentStatus = stateManager.getCurrentStatus(liveId);
            if (!stateManager.canTransitionTo(currentStatus, LiveStateManager.LiveStatus.ENDED)) {
                return R.fail("当前状态不允许结束直播");
            }

            // 2. 更新直播状态
            stateManager.updateLiveStatus(liveId, LiveStateManager.LiveStatus.ENDED);

            // 3. 更新直播结束时间和时长
            LiveVo liveInfo = liveService.queryById(liveId);
            if (liveInfo != null && liveInfo.getActualStartTime() != null) {
                java.util.Date endTime = new java.util.Date();
                long durationMinutes = (endTime.getTime() - liveInfo.getActualStartTime().getTime()) / (1000 * 60);

                LiveBo updateBo = new LiveBo();
                updateBo.setId(liveId);
                updateBo.setActualEndTime(endTime);
                updateBo.setDurationMinutes(durationMinutes);
                liveService.updateByBo(updateBo);
            }

            log.info("直播结束成功，直播ID: {}", liveId);
            return R.ok();

        } catch (Exception e) {
            log.error("结束直播失败，直播ID: {}", liveId, e);
            return R.fail("结束直播失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询直播列表（包含完整信息）
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 分页结果
     */
    public TableDataInfo<Map<String, Object>> queryCompletePageList(LiveBo bo, PageQuery pageQuery) {
        try {
            log.info("分页查询完整直播列表");
            return liveService.queryPageListWithDetails(bo, pageQuery);
        } catch (Exception e) {
            log.error("分页查询完整直播列表失败", e);
            throw new RuntimeException("查询失败: " + e.getMessage());
        }
    }

    /**
     * 刷新推流地址
     *
     * @param liveId 直播ID
     * @return 新的推流信息
     */
    @Transactional(rollbackFor = Exception.class)
    public R<LiveStreamInfo> refreshStreamInfo(Long liveId) {
        try {
            log.info("刷新推流地址，直播ID: {}", liveId);

            // 1. 验证直播是否存在
            LiveVo liveInfo = liveService.queryById(liveId);
            if (liveInfo == null) {
                return R.fail("直播不存在");
            }

            // 2. 重新生成推流信息
            LiveStreamInfo newStreamInfo = liveStreamService.createStreamForLive(liveId, liveInfo.getTitle());
            if (newStreamInfo == null) {
                return R.fail("生成推流信息失败");
            }

            log.info("推流地址刷新成功，直播ID: {}, 新推流ID: {}", liveId, newStreamInfo.getId());
            return R.ok(newStreamInfo);

        } catch (Exception e) {
            log.error("刷新推流地址失败，直播ID: {}", liveId, e);
            return R.fail("刷新推流地址失败: " + e.getMessage());
        }
    }
}
