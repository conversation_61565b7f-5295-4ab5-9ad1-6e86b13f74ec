package com.ydwl.live.service.stream;

import com.ydwl.live.config.ConfigManager;
import com.ydwl.live.domain.LiveStream;
import com.ydwl.live.mapper.LiveStreamMapper;
import com.ydwl.live.service.impl.LiveStateManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 推流监控服务
 * 实时监控推流状态，处理推流异常
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StreamMonitorService {

    private final LiveStreamMapper liveStreamMapper;
    private final LiveStateManager stateManager;
    private final ConfigManager configManager;
    private final StreamHealthChecker streamHealthChecker;
    private final StreamAlertService streamAlertService;

    /**
     * 定时检查推流状态
     */
    @Scheduled(fixedDelayString = "#{@configManager.getTaskConfig().statusCheckInterval * 1000}")
    public void checkStreamStatus() {
        if (!configManager.getTaskConfig().isEnabled()) {
            return;
        }

        log.debug("开始检查推流状态");
        
        try {
            // 获取所有活跃的推流
            List<LiveStream> activeStreams = getActiveStreams();
            
            for (LiveStream stream : activeStreams) {
                checkSingleStreamAsync(stream);
            }
            
        } catch (Exception e) {
            log.error("检查推流状态失败", e);
        }
    }

    /**
     * 异步检查单个推流状态
     */
    @Async
    public CompletableFuture<Void> checkSingleStreamAsync(LiveStream stream) {
        try {
            StreamHealthStatus status = streamHealthChecker.checkStreamHealth(stream);
            handleStreamStatus(stream, status);
        } catch (Exception e) {
            log.error("检查推流状态失败，StreamId: {}", stream.getId(), e);
        }
        return CompletableFuture.completedFuture(null);
    }

    /**
     * 处理推流状态
     */
    private void handleStreamStatus(LiveStream stream, StreamHealthStatus status) {
        switch (status.getStatus()) {
            case HEALTHY:
                handleHealthyStream(stream, status);
                break;
            case UNSTABLE:
                handleUnstableStream(stream, status);
                break;
            case DISCONNECTED:
                handleDisconnectedStream(stream, status);
                break;
            case ERROR:
                handleErrorStream(stream, status);
                break;
        }
    }

    /**
     * 处理健康的推流
     */
    private void handleHealthyStream(LiveStream stream, StreamHealthStatus status) {
        // 更新推流质量指标
        updateStreamMetrics(stream, status);
        
        // 如果之前有异常，现在恢复了，发送恢复通知
        if (stream.getStreamStatus() != 1L) {
            stream.setStreamStatus(1L);
            liveStreamMapper.updateById(stream);
            
            streamAlertService.sendStreamRecoveryAlert(stream, status);
            log.info("推流已恢复正常，StreamId: {}, LiveId: {}", stream.getId(), stream.getLiveId());
        }
    }

    /**
     * 处理不稳定的推流
     */
    private void handleUnstableStream(LiveStream stream, StreamHealthStatus status) {
        log.warn("推流不稳定，StreamId: {}, LiveId: {}, 原因: {}", 
                stream.getId(), stream.getLiveId(), status.getMessage());
        
        // 发送不稳定警告
        streamAlertService.sendStreamUnstableAlert(stream, status);
        
        // 更新状态为警告
        if (stream.getStreamStatus() != 3L) {
            stream.setStreamStatus(3L); // 警告状态
            liveStreamMapper.updateById(stream);
        }
    }

    /**
     * 处理断开的推流
     */
    private void handleDisconnectedStream(LiveStream stream, StreamHealthStatus status) {
        log.warn("推流已断开，StreamId: {}, LiveId: {}", stream.getId(), stream.getLiveId());
        
        // 检查是否超过超时时间
        long timeoutSeconds = configManager.getStreamConfig().getPushTimeout();
        long disconnectedTime = (System.currentTimeMillis() - status.getLastActiveTime()) / 1000;
        
        if (disconnectedTime > timeoutSeconds) {
            // 超时，自动结束直播
            autoEndLive(stream, "推流超时自动结束");
        } else {
            // 发送断开警告
            streamAlertService.sendStreamDisconnectedAlert(stream, status);
            
            // 更新状态为断开
            stream.setStreamStatus(2L); // 异常状态
            liveStreamMapper.updateById(stream);
        }
    }

    /**
     * 处理错误的推流
     */
    private void handleErrorStream(LiveStream stream, StreamHealthStatus status) {
        log.error("推流发生错误，StreamId: {}, LiveId: {}, 错误: {}", 
                stream.getId(), stream.getLiveId(), status.getMessage());
        
        // 发送错误警告
        streamAlertService.sendStreamErrorAlert(stream, status);
        
        // 更新状态为错误
        stream.setStreamStatus(2L); // 异常状态
        liveStreamMapper.updateById(stream);
        
        // 如果是严重错误，考虑自动结束直播
        if (status.isCriticalError()) {
            autoEndLive(stream, "推流严重错误自动结束: " + status.getMessage());
        }
    }

    /**
     * 自动结束直播
     */
    private void autoEndLive(LiveStream stream, String reason) {
        try {
            LiveStateManager.LiveStatus currentStatus = stateManager.getCurrentStatus(stream.getLiveId());
            if (currentStatus == LiveStateManager.LiveStatus.LIVE) {
                boolean success = stateManager.updateLiveStatus(stream.getLiveId(), LiveStateManager.LiveStatus.ENDED);
                if (success) {
                    log.info("自动结束直播成功，LiveId: {}, 原因: {}", stream.getLiveId(), reason);
                    streamAlertService.sendAutoEndLiveAlert(stream, reason);
                } else {
                    log.error("自动结束直播失败，LiveId: {}", stream.getLiveId());
                }
            }
        } catch (Exception e) {
            log.error("自动结束直播异常，LiveId: {}", stream.getLiveId(), e);
        }
    }

    /**
     * 更新推流质量指标
     */
    private void updateStreamMetrics(LiveStream stream, StreamHealthStatus status) {
        // 这里可以记录推流质量指标到数据库或监控系统
        // 比如：码率、帧率、延迟等
        log.debug("更新推流质量指标，StreamId: {}, 码率: {}kbps, 帧率: {}fps", 
                stream.getId(), status.getBitrate(), status.getFrameRate());
    }

    /**
     * 获取所有活跃的推流
     */
    private List<LiveStream> getActiveStreams() {
        // 查询状态为正常或警告的推流
        return liveStreamMapper.selectList(
            new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<LiveStream>()
                .in(LiveStream::getStreamStatus, 1L, 3L) // 正常或警告状态
                .isNull(LiveStream::getDelFlag)
        );
    }

    /**
     * 推流健康状态
     */
    public static class StreamHealthStatus {
        public enum Status {
            HEALTHY,    // 健康
            UNSTABLE,   // 不稳定
            DISCONNECTED, // 断开
            ERROR       // 错误
        }

        private Status status;
        private String message;
        private long lastActiveTime;
        private double bitrate;
        private double frameRate;
        private boolean criticalError;

        // 构造函数和getter/setter
        public StreamHealthStatus(Status status, String message) {
            this.status = status;
            this.message = message;
            this.lastActiveTime = System.currentTimeMillis();
        }

        // Getters and Setters
        public Status getStatus() { return status; }
        public void setStatus(Status status) { this.status = status; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public long getLastActiveTime() { return lastActiveTime; }
        public void setLastActiveTime(long lastActiveTime) { this.lastActiveTime = lastActiveTime; }
        public double getBitrate() { return bitrate; }
        public void setBitrate(double bitrate) { this.bitrate = bitrate; }
        public double getFrameRate() { return frameRate; }
        public void setFrameRate(double frameRate) { this.frameRate = frameRate; }
        public boolean isCriticalError() { return criticalError; }
        public void setCriticalError(boolean criticalError) { this.criticalError = criticalError; }
    }
}
