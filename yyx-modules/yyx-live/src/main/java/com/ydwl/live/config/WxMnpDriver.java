package com.ydwl.live.config;


import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceImpl;
import cn.binarywang.wx.miniapp.config.impl.WxMaDefaultConfigImpl;
import com.ydwl.live.config.ConfigManager;
import com.ydwl.live.config.UnifiedLiveConfig;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 微信基础驱动
 */
@Configuration
@RequiredArgsConstructor
public class WxMnpDriver {

    private final ConfigManager configManager;

    /**
     * 微信小程序
     *
     * @return WxMaService
     */
    @Bean
    public WxMaService wxMaService() {
        UnifiedLiveConfig.WxMiniappConfig wxConfig = configManager.getWxMiniappConfig();

        WxMaDefaultConfigImpl config = new WxMaDefaultConfigImpl();
        config.setAppid(wxConfig.getAppid());
        config.setSecret(wxConfig.getSecret());
        config.setToken(wxConfig.getToken());
        config.setAesKey(wxConfig.getAesKey());
        config.setMsgDataFormat(wxConfig.getMsgDataFormat());

        WxMaService wxMaService = new WxMaServiceImpl();
        wxMaService.setWxMaConfig(config);
        return wxMaService;
    }

}
