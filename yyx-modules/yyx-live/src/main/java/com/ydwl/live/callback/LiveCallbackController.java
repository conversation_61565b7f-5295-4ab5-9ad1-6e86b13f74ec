package com.ydwl.live.callback;

import com.ydwl.common.core.domain.R;
import com.ydwl.common.log.annotation.Log;
import com.ydwl.common.log.enums.BusinessType;
import com.ydwl.live.callback.dto.LiveCallbackDto;
import com.ydwl.live.callback.service.LiveCallbackService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 直播回调控制器
 * 处理阿里云直播的各种回调通知
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Slf4j
@RestController
@RequestMapping("/live/callback")
@RequiredArgsConstructor
public class LiveCallbackController {

    private final LiveCallbackService liveCallbackService;

    /**
     * 推流开始回调
     */
    @Log(title = "推流开始回调", businessType = BusinessType.OTHER)
    @PostMapping("/stream/start")
    public R<String> onStreamStart(@RequestBody LiveCallbackDto callbackDto) {
        try {
            log.info("收到推流开始回调: {}", callbackDto);
            liveCallbackService.handleStreamStart(callbackDto);
            return R.ok("推流开始回调处理成功");
        } catch (Exception e) {
            log.error("处理推流开始回调失败", e);
            return R.fail("处理推流开始回调失败: " + e.getMessage());
        }
    }

    /**
     * 推流结束回调
     */
    @Log(title = "推流结束回调", businessType = BusinessType.OTHER)
    @PostMapping("/stream/stop")
    public R<String> onStreamStop(@RequestBody LiveCallbackDto callbackDto) {
        try {
            log.info("收到推流结束回调: {}", callbackDto);
            liveCallbackService.handleStreamStop(callbackDto);
            return R.ok("推流结束回调处理成功");
        } catch (Exception e) {
            log.error("处理推流结束回调失败", e);
            return R.fail("处理推流结束回调失败: " + e.getMessage());
        }
    }

    /**
     * 录制开始回调
     */
    @Log(title = "录制开始回调", businessType = BusinessType.OTHER)
    @PostMapping("/record/start")
    public R<String> onRecordStart(@RequestBody LiveCallbackDto callbackDto) {
        try {
            log.info("收到录制开始回调: {}", callbackDto);
            liveCallbackService.handleRecordStart(callbackDto);
            return R.ok("录制开始回调处理成功");
        } catch (Exception e) {
            log.error("处理录制开始回调失败", e);
            return R.fail("处理录制开始回调失败: " + e.getMessage());
        }
    }

    /**
     * 录制结束回调
     */
    @Log(title = "录制结束回调", businessType = BusinessType.OTHER)
    @PostMapping("/record/stop")
    public R<String> onRecordStop(@RequestBody LiveCallbackDto callbackDto) {
        try {
            log.info("收到录制结束回调: {}", callbackDto);
            liveCallbackService.handleRecordStop(callbackDto);
            return R.ok("录制结束回调处理成功");
        } catch (Exception e) {
            log.error("处理录制结束回调失败", e);
            return R.fail("处理录制结束回调失败: " + e.getMessage());
        }
    }

    /**
     * 截图回调
     */
    @Log(title = "截图回调", businessType = BusinessType.OTHER)
    @PostMapping("/snapshot")
    public R<String> onSnapshot(@RequestBody LiveCallbackDto callbackDto) {
        try {
            log.info("收到截图回调: {}", callbackDto);
            liveCallbackService.handleSnapshot(callbackDto);
            return R.ok("截图回调处理成功");
        } catch (Exception e) {
            log.error("处理截图回调失败", e);
            return R.fail("处理截图回调失败: " + e.getMessage());
        }
    }

    /**
     * 通用回调接口（兼容旧版本）
     */
    @Log(title = "通用直播回调", businessType = BusinessType.OTHER)
    @PostMapping("/universal")
    public R<String> onUniversalCallback(@RequestBody Map<String, Object> callbackData) {
        try {
            log.info("收到通用直播回调: {}", callbackData);
            
            String action = (String) callbackData.get("action");
            if (action == null) {
                action = (String) callbackData.get("event");
            }
            
            if (action != null) {
                LiveCallbackDto callbackDto = LiveCallbackDto.fromMap(callbackData);
                
                switch (action.toLowerCase()) {
                    case "publish":
                    case "stream_start":
                        liveCallbackService.handleStreamStart(callbackDto);
                        break;
                    case "publish_done":
                    case "stream_stop":
                        liveCallbackService.handleStreamStop(callbackDto);
                        break;
                    case "record_start":
                        liveCallbackService.handleRecordStart(callbackDto);
                        break;
                    case "record_stop":
                        liveCallbackService.handleRecordStop(callbackDto);
                        break;
                    case "snapshot":
                        liveCallbackService.handleSnapshot(callbackDto);
                        break;
                    default:
                        log.warn("未知的回调动作: {}", action);
                        return R.fail("未知的回调动作: " + action);
                }
            }
            
            return R.ok("通用回调处理成功");
        } catch (Exception e) {
            log.error("处理通用回调失败", e);
            return R.fail("处理通用回调失败: " + e.getMessage());
        }
    }

    /**
     * 按需录制回调（返回是否需要录制）
     */
    @PostMapping("/record/demand")
    public Map<String, Object> onDemandRecord(@RequestBody Map<String, Object> params) {
        try {
            String streamId = (String) params.get("stream");
            log.info("收到按需录制查询，StreamId: {}", streamId);
            
            boolean needRecord = liveCallbackService.shouldRecord(streamId);
            
            Map<String, Object> response = Map.of(
                "needRecord", needRecord,
                "message", needRecord ? "需要录制" : "不需要录制"
            );
            
            log.info("按需录制查询结果，StreamId: {}, 需要录制: {}", streamId, needRecord);
            return response;
            
        } catch (Exception e) {
            log.error("处理按需录制查询失败", e);
            return Map.of(
                "needRecord", false,
                "message", "查询失败: " + e.getMessage()
            );
        }
    }
}
