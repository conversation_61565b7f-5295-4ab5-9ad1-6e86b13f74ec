package com.ydwl.live.util;


import com.aliyun.live20161101.models.*;
import com.aliyun.teaopenapi.models.Config;
import com.ydwl.LiveTranscoding.config.GlobalConfigProperties;
import com.ydwl.live.config.ConfigManager;

import jakarta.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.aliyun.live20161101.Client;
/**
 * 阿里云直播转码配置管理工具类
 * 功能说明：
 * 1. 提供直播转码配置的增删查操作
 * 2. 封装阿里云直播服务的转码API接口
 * 3. 使用Spring单例模式管理客户端实例保证线程安全
 * 注意事项：
 * - 需确保阿里云账号已开通直播服务
 * - 配置参数需与阿里云控制台保持一致
 * - 转码模板需提前用AliLiveRecordUtil.addLiveRecordConfig()方法添加
 *
 * <AUTHOR>
 * @date 2025-03.26
 */
@Component
public class AliyunLiveStreamUtil {

    @Autowired
    private ConfigManager configManager;

    private static Client client;

    /**
     * 初始化阿里云直播客户端
     * 在Spring容器初始化后自动执行，用于配置阿里云直播服务的AccessKey和Region
     */
    @PostConstruct
    public void init() {
        try {
            Config config = new Config()
                    .setAccessKeyId(configManager.getAliyunConfig().getAccessKeyId())
                    .setAccessKeySecret(configManager.getAliyunConfig().getAccessKeySecret())
                    .setRegionId(configManager.getAliyunConfig().getRegionId());
            client = new Client(config);
        } catch (Exception e) {
            throw new RuntimeException("Failed to initialize Aliyun Live Stream client", e);
        }
    }

    // =====================================================================================
    // 转码配置管理方法
    // =====================================================================================

    /**
     * 添加直播转码配置
     *
     * @param request 转码配置请求对象
     * @return 转码配置响应对象
     * @throws Exception 网络或API调用异常
     */
    public static AddLiveStreamTranscodeResponse AddLiveStreamTranscode(AddLiveStreamTranscodeRequest request) throws Exception {

        AddLiveStreamTranscodeRequest addLiveStreamTranscodeRequest = new AddLiveStreamTranscodeRequest();
        addLiveStreamTranscodeRequest.setDomain(request.getDomain());
        addLiveStreamTranscodeRequest.setApp(request.getApp());
        addLiveStreamTranscodeRequest.setLazy(request.getLazy());
        addLiveStreamTranscodeRequest.setTemplate(request.getTemplate());
        // 执行API调用
        return client.addLiveStreamTranscode(addLiveStreamTranscodeRequest);
    }

    /**
     * 删除直播转码配置
     *
     * @param request 转码配置删除请求对象
     * @return 删除操作响应对象
     * @throws Exception 网络或API调用异常
     */
    public static DeleteLiveStreamTranscodeResponse DeleteLiveStreamTranscode(DeleteLiveStreamTranscodeRequest request) throws Exception {
        DeleteLiveStreamTranscodeRequest req = new DeleteLiveStreamTranscodeRequest();
        req.setDomain(request.getDomain());
        req.setApp(request.getApp());
        req.setTemplate(request.getTemplate());

        // 执行删除操作
        return client.deleteLiveStreamTranscode(req);
    }

    /**
     * 查询直播转码配置信息
     *
     * @param request 查询请求对象
     * @return 转码配置信息响应对象
     * @throws Exception 网络或API调用异常
     */
    public static DescribeLiveStreamTranscodeInfoResponse DescribeLiveStreamTranscodeInfo(DescribeLiveStreamTranscodeInfoRequest request) throws Exception {
        DescribeLiveStreamTranscodeInfoRequest req = new DescribeLiveStreamTranscodeInfoRequest();
        req.setDomainTranscodeName(request.getDomainTranscodeName());
        req.setOwnerId(request.getOwnerId());
        // 执行查询操作
        return client.describeLiveStreamTranscodeInfo(req);
    }
}
