package com.ydwl.live.service.state;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 直播状态机
 * 管理直播状态的转换逻辑和规则
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Slf4j
@Component
public class LiveStateMachine {

    /**
     * 直播状态枚举
     */
    public enum LiveStatus {
        CREATED(0, "已创建"),
        SCHEDULED(1, "已预约"),
        LIVE(2, "直播中"),
        ENDED(3, "已结束"),
        REPLAY(4, "回放中"),
        CANCELLED(5, "已取消");

        private final int code;
        private final String description;

        LiveStatus(int code, String description) {
            this.code = code;
            this.description = description;
        }

        public int getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static LiveStatus fromCode(int code) {
            for (LiveStatus status : values()) {
                if (status.code == code) {
                    return status;
                }
            }
            throw new IllegalArgumentException("未知的状态码: " + code);
        }
    }

    /**
     * 状态转换规则映射
     */
    private static final Map<LiveStatus, Set<LiveStatus>> TRANSITION_RULES = new HashMap<>();

    static {
        // 已创建 -> 已预约、直播中、已取消
        TRANSITION_RULES.put(LiveStatus.CREATED, Set.of(
            LiveStatus.SCHEDULED, LiveStatus.LIVE, LiveStatus.CANCELLED
        ));

        // 已预约 -> 直播中、已取消
        TRANSITION_RULES.put(LiveStatus.SCHEDULED, Set.of(
            LiveStatus.LIVE, LiveStatus.CANCELLED
        ));

        // 直播中 -> 已结束
        TRANSITION_RULES.put(LiveStatus.LIVE, Set.of(
            LiveStatus.ENDED
        ));

        // 已结束 -> 回放中
        TRANSITION_RULES.put(LiveStatus.ENDED, Set.of(
            LiveStatus.REPLAY
        ));

        // 回放中 -> 无后续状态
        TRANSITION_RULES.put(LiveStatus.REPLAY, Set.of());

        // 已取消 -> 无后续状态
        TRANSITION_RULES.put(LiveStatus.CANCELLED, Set.of());
    }

    /**
     * 当前直播状态缓存
     */
    private final Map<Long, LiveStatus> currentStatusCache = new ConcurrentHashMap<>();

    /**
     * 状态变更监听器
     */
    private final List<LiveStatusChangeListener> listeners = new ArrayList<>();

    /**
     * 检查状态转换是否合法
     *
     * @param from 源状态
     * @param to   目标状态
     * @return 是否可以转换
     */
    public boolean canTransition(LiveStatus from, LiveStatus to) {
        if (from == null || to == null) {
            return false;
        }

        Set<LiveStatus> allowedTransitions = TRANSITION_RULES.get(from);
        return allowedTransitions != null && allowedTransitions.contains(to);
    }

    /**
     * 执行状态转换
     *
     * @param liveId 直播ID
     * @param from   源状态
     * @param to     目标状态
     * @return 转换是否成功
     */
    public boolean transition(Long liveId, LiveStatus from, LiveStatus to) {
        if (!canTransition(from, to)) {
            log.warn("非法的状态转换，直播ID: {}, 从 {} 到 {}", liveId, from, to);
            return false;
        }

        try {
            // 执行状态转换前的处理
            beforeTransition(liveId, from, to);

            // 更新状态缓存
            currentStatusCache.put(liveId, to);

            // 执行状态转换后的处理
            afterTransition(liveId, from, to);

            // 通知监听器
            notifyListeners(liveId, from, to);

            log.info("状态转换成功，直播ID: {}, 从 {} 到 {}", liveId, from, to);
            return true;

        } catch (Exception e) {
            log.error("状态转换失败，直播ID: {}, 从 {} 到 {}", liveId, from, to, e);
            return false;
        }
    }

    /**
     * 获取当前状态
     *
     * @param liveId 直播ID
     * @return 当前状态
     */
    public LiveStatus getCurrentStatus(Long liveId) {
        return currentStatusCache.get(liveId);
    }

    /**
     * 设置当前状态（用于初始化）
     *
     * @param liveId 直播ID
     * @param status 状态
     */
    public void setCurrentStatus(Long liveId, LiveStatus status) {
        currentStatusCache.put(liveId, status);
    }

    /**
     * 获取可转换的状态列表
     *
     * @param currentStatus 当前状态
     * @return 可转换的状态列表
     */
    public Set<LiveStatus> getAvailableTransitions(LiveStatus currentStatus) {
        return TRANSITION_RULES.getOrDefault(currentStatus, Set.of());
    }

    /**
     * 添加状态变更监听器
     *
     * @param listener 监听器
     */
    public void addListener(LiveStatusChangeListener listener) {
        listeners.add(listener);
    }

    /**
     * 状态转换前的处理
     *
     * @param liveId 直播ID
     * @param from   源状态
     * @param to     目标状态
     */
    private void beforeTransition(Long liveId, LiveStatus from, LiveStatus to) {
        log.debug("状态转换前处理，直播ID: {}, 从 {} 到 {}", liveId, from, to);

        // 根据不同的状态转换执行相应的前置处理
        switch (to) {
            case LIVE:
                // 开始直播前的处理
                handleBeforeGoLive(liveId);
                break;
            case ENDED:
                // 结束直播前的处理
                handleBeforeEndLive(liveId);
                break;
            case REPLAY:
                // 开始回放前的处理
                handleBeforeStartReplay(liveId);
                break;
            default:
                break;
        }
    }

    /**
     * 状态转换后的处理
     *
     * @param liveId 直播ID
     * @param from   源状态
     * @param to     目标状态
     */
    private void afterTransition(Long liveId, LiveStatus from, LiveStatus to) {
        log.debug("状态转换后处理，直播ID: {}, 从 {} 到 {}", liveId, from, to);

        // 根据不同的状态转换执行相应的后置处理
        switch (to) {
            case LIVE:
                // 开始直播后的处理
                handleAfterGoLive(liveId);
                break;
            case ENDED:
                // 结束直播后的处理
                handleAfterEndLive(liveId);
                break;
            case REPLAY:
                // 开始回放后的处理
                handleAfterStartReplay(liveId);
                break;
            default:
                break;
        }
    }

    /**
     * 通知监听器
     *
     * @param liveId 直播ID
     * @param from   源状态
     * @param to     目标状态
     */
    private void notifyListeners(Long liveId, LiveStatus from, LiveStatus to) {
        for (LiveStatusChangeListener listener : listeners) {
            try {
                listener.onStatusChange(liveId, from, to);
            } catch (Exception e) {
                log.error("状态变更监听器执行失败，直播ID: {}", liveId, e);
            }
        }
    }

    // 具体的状态处理方法
    private void handleBeforeGoLive(Long liveId) {
        log.info("准备开始直播，直播ID: {}", liveId);
    }

    private void handleAfterGoLive(Long liveId) {
        log.info("直播已开始，直播ID: {}", liveId);
    }

    private void handleBeforeEndLive(Long liveId) {
        log.info("准备结束直播，直播ID: {}", liveId);
    }

    private void handleAfterEndLive(Long liveId) {
        log.info("直播已结束，直播ID: {}", liveId);
    }

    private void handleBeforeStartReplay(Long liveId) {
        log.info("准备开始回放，直播ID: {}", liveId);
    }

    private void handleAfterStartReplay(Long liveId) {
        log.info("回放已开始，直播ID: {}", liveId);
    }

    /**
     * 状态变更监听器接口
     */
    public interface LiveStatusChangeListener {
        void onStatusChange(Long liveId, LiveStatus from, LiveStatus to);
    }
}
