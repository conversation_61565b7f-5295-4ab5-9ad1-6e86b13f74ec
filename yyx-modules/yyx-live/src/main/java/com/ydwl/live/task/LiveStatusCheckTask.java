package com.ydwl.live.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ydwl.live.config.ConfigManager;
import com.ydwl.live.domain.Live;
import com.ydwl.live.domain.LiveStream;
import com.ydwl.live.mapper.LiveMapper;
import com.ydwl.live.mapper.LiveStreamMapper;
import com.ydwl.live.service.impl.LiveStateManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 直播状态检查定时任务
 * 定期检查直播状态，处理异常情况和超时直播
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "ydwl.live.task", name = "enabled", havingValue = "true")
public class LiveStatusCheckTask {

    private final LiveMapper liveMapper;
    private final LiveStreamMapper liveStreamMapper;
    private final LiveStateManager stateManager;
    private final ConfigManager configManager;

    /**
     * 定时检查直播状态
     * 检查长时间无推流的直播，自动结束异常直播
     */
    @Scheduled(fixedRateString = "${ydwl.live.task.status-check-interval:60}", timeUnit = TimeUnit.SECONDS)
    public void checkLiveStatus() {
        log.info("开始执行直播状态检查任务");
        try {
            // 查找状态为直播中的直播
            List<Live> livesInProgress = liveMapper.selectList(new LambdaQueryWrapper<Live>()
                .eq(Live::getStatus, LiveStateManager.LiveStatus.LIVE.getValue()));

            if (livesInProgress.isEmpty()) {
                log.info("当前没有正在进行的直播");
                return;
            }

            log.info("发现{}个正在进行的直播，开始检查", livesInProgress.size());
            for (Live live : livesInProgress) {
                checkSingleLive(live);
            }
        } catch (Exception e) {
            log.error("直播状态检查任务异常", e);
        }
    }

    /**
     * 检查单个直播的状态
     *
     * @param live 直播信息
     */
    private void checkSingleLive(Live live) {
        Long liveId = live.getId();
        try {
            // 查询推流状态
            LiveStream stream = liveStreamMapper.selectOne(new LambdaQueryWrapper<LiveStream>()
                .eq(LiveStream::getLiveId, liveId));

            if (stream == null) {
                log.warn("直播({})无推流信息，可能需要手动结束", liveId);
                return;
            }

            // 检查直播是否超时
            if (isLiveTimeout(live, stream)) {
                log.info("直播({})超时无推流，自动结束", liveId);
                stateManager.updateLiveStatus(liveId, LiveStateManager.LiveStatus.ENDED);
            }
        } catch (Exception e) {
            log.error("检查直播({})状态异常", liveId, e);
        }
    }

    /**
     * 判断直播是否超时
     * 根据配置的超时时间和更新时间判断
     *
     * @param live   直播信息
     * @param stream 推流信息
     * @return 是否超时
     */
    private boolean isLiveTimeout(Live live, LiveStream stream) {
        // 获取推流超时时间（秒）
        long timeoutSeconds = configManager.getStreamConfig().getPushTimeout();
        
        // 计算最后更新时间到现在的时间差
        Date lastUpdateTime = stream.getUpdateTime();
        if (lastUpdateTime == null) {
            lastUpdateTime = live.getUpdateTime();
        }
        
        if (lastUpdateTime == null) {
            return false;
        }
        
        long timeDiffSeconds = (System.currentTimeMillis() - lastUpdateTime.getTime()) / 1000;
        
        // 如果超过配置的超时时间，认为直播已超时
        return timeDiffSeconds > timeoutSeconds;
    }
} 