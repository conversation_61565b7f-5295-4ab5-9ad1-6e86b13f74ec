# Spring配置
spring:
  application:
    name: ydwl-live
  profiles:
    active: dev
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 100MB
      # 设置总上传的文件大小
      max-request-size: 200MB
  # jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      # 格式化输出
      indent_output: false
      # 忽略无法转换的对象
      fail_on_empty_beans: false
    deserialization:
      # 允许对象忽略json中不存在的属性
      fail_on_unknown_properties: false

# MyBatis配置
mybatis-plus:
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  # 实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.ydwl.**.domain
  global-config:
    # 数据库相关配置
    db-config:
      # 主键类型  AUTO:"数据库ID自增", INPUT:"用户输入ID", ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
      id-type: AUTO
      # 逻辑删除字段名
      logic-delete-field: delFlag
      # 逻辑删除字面值：未删除为0
      logic-not-delete-value: 0
      # 逻辑删除字面值：删除为1
      logic-delete-value: 1
      # 更新策略 IGNORED:"忽略判断",NOT_NULL:"非 NULL 判断"),NOT_EMPTY:"非空判断"
      update-strategy: NOT_NULL
  # 原生配置
  configuration:
    # 控制台打印完整带参数SQL语句
    # log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # 开启驼峰命名法映射
    map-underscore-to-camel-case: true
    # 返回类型为Map,显示null对应的字段
    call-setters-on-nulls: true
    # 返回map的值不做自动映射
    auto-mapping-behavior: full
    # 设置当查询结果值为null时是否调用映射对象的setter方法
    return-instance-for-empty-row: false

# 直播服务统一配置
ydwl:
  live:
    # 推流配置
    stream:
      # 推流域名
      push-domain: push.live.ycyyx.com
      # 播放域名
      play-domain: play.live.ycyyx.com
      # 应用名称
      app-name: live
      # 推流鉴权密钥
      push-secret-key: 2p5n2R8aA6tTQtu2
      # 播放鉴权密钥
      play-secret-key: LznJujBX81mOJM7E
      # 播放URL有效期（秒）
      play-url-expire-seconds: 86400
      # 推流超时时间（秒）
      push-timeout: 300

    # 任务配置
    task:
      # 是否启用定时任务
      enabled: true
      # 直播状态检查间隔（秒）
      status-check-interval: 60
      # 录制检查间隔（秒）
      record-check-interval: 120

    # 认证配置
    auth:
      # token过期时间(单位:秒)
      token-timeout: 86400
      # 是否启用访问控制
      access-control-enabled: true

    # 视频处理配置
    video:
      # 最大文件大小（MB）
      max-file-size: 2048
      # 支持的视频格式
      supported-formats: ["mp4", "flv", "m3u8", "mov"]
      # 转码重试次数
      max-retry-count: 3
      # 重试间隔（秒）
      retry-interval: 300
      # 转码超时时间（秒）
      transcode-timeout: 7200
      # 是否异步转码
      async-transcode: true
      # 转码任务队列大小
      transcode-queue-size: 100
      # 转码线程池大小
      transcode-thread-pool-size: 5

    # 缓存配置
    cache:
      # 是否启用缓存
      enabled: true
      # 直播信息缓存时间（秒）
      live-info-cache-time: 300
      # 推流信息缓存时间（秒）
      stream-info-cache-time: 600
      # 用户信息缓存时间（秒）
      user-info-cache-time: 1800
      # 默认缓存过期时间（分钟）
      default-ttl-minutes: 10
      # 是否缓存空值
      cache-null-values: false

    # OSS配置
    oss:
      # 访问密钥ID
      access-key: ${ALIYUN_OSS_ACCESS_KEY:}
      # 访问密钥密码
      secret-key: ${ALIYUN_OSS_SECRET_KEY:}
      # 地域节点
      endpoint: ${ALIYUN_OSS_ENDPOINT:}
      # 存储桶名称
      bucket-name: ${ALIYUN_OSS_BUCKET:}
      # 消息通知Topic
      notify-topic: ${ALIYUN_OSS_NOTIFY_TOPIC:}
      # OSS回调URL
      callback-url: ${ALIYUN_OSS_CALLBACK_URL:}
      # 默认最大文件大小（字节）
      max-file-size: 2147483648  # 2GB
      # CDN域名
      cdn-domain: ${ALIYUN_OSS_CDN_DOMAIN:}

    # 阿里云配置
    aliyun:
      # 阿里云AccessKeyId
      access-key-id: ${ALIYUN_ACCESS_KEY_ID:}
      # 阿里云AccessKeySecret
      access-key-secret: ${ALIYUN_ACCESS_KEY_SECRET:}
      # 阿里云直播服务地域ID
      region-id: ${ALIYUN_REGION_ID:cn-beijing}
      # 阿里云直播推流域名
      domain-name: ${ALIYUN_LIVE_DOMAIN:}
      # 阿里云OSS Endpoint
      oss-endpoint: ${ALIYUN_OSS_ENDPOINT:}
      # 阿里云OSS Bucket名称
      oss-bucket: ${ALIYUN_OSS_BUCKET:}

    # 微信小程序配置
    wx-miniapp:
      # 微信小程序的 appid
      appid: ${WX_MINIAPP_APPID:}
      # 微信小程序的 app secret
      secret: ${WX_MINIAPP_SECRET:}
      # 微信小程序消息服务器配置的token
      token: ${WX_MINIAPP_TOKEN:}
      # 微信小程序消息服务器配置的EncodingAESKey
      aes-key: ${WX_MINIAPP_AES_KEY:}
      # 消息格式，XML或者JSON
      msg-data-format: JSON

    # 配置迁移
    config:
      migration:
        # 是否启用配置迁移（仅在迁移期间启用）
        enabled: false

# 微信小程序配置
wx:
  miniapp:
    # 微信小程序的 appid
    appid: 你的appid
    # 微信小程序的 app secret
    secret: 你的app secret
    # 微信小程序消息服务器配置的token
    token: 你的token
    # 微信小程序消息服务器配置的EncodingAESKey
    aesKey: 你的EncodingAESKey
    # 消息格式，XML或者JSON
    msgDataFormat: JSON 