# 数据库优化建议

## 1. 索引优化

### 直播表 (live)
```sql
-- 复合索引：状态 + 创建时间（用于状态查询和时间排序）
CREATE INDEX idx_live_status_create_time ON live(status, create_time);

-- 复合索引：分类 + 状态（用于分类筛选）
CREATE INDEX idx_live_category_status ON live(category_id, status);

-- 复合索引：用户 + 状态（用于用户直播列表）
CREATE INDEX idx_live_user_status ON live(create_by, status);

-- 时间范围查询索引
CREATE INDEX idx_live_time_range ON live(plan_start_time, actual_start_time);
```

### 推流表 (live_stream)
```sql
-- 直播ID索引（一对一关系）
CREATE UNIQUE INDEX idx_stream_live_id ON live_stream(live_id);

-- 状态索引
CREATE INDEX idx_stream_status ON live_stream(stream_status);
```

### 回放表 (live_replay)
```sql
-- 直播ID索引（一对多关系）
CREATE INDEX idx_replay_live_id ON live_replay(live_id);

-- 状态 + 创建时间索引
CREATE INDEX idx_replay_status_time ON live_replay(status, create_time);
```

### 转码任务表 (live_transcode_task)
```sql
-- 业务ID索引
CREATE INDEX idx_transcode_biz_id ON live_transcode_task(biz_id);

-- 状态 + 创建时间索引
CREATE INDEX idx_transcode_status_time ON live_transcode_task(status, create_time);

-- 文件路径索引（用于查重）
CREATE INDEX idx_transcode_file_path ON live_transcode_task(input_bucket, input_object);
```

## 2. 分区策略

### 按时间分区
```sql
-- 直播表按月分区
ALTER TABLE live PARTITION BY RANGE (YEAR(create_time) * 100 + MONTH(create_time)) (
    PARTITION p202501 VALUES LESS THAN (202502),
    PARTITION p202502 VALUES LESS THAN (202503),
    PARTITION p202503 VALUES LESS THAN (202504),
    -- ... 继续添加分区
    PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- 转码任务表按月分区
ALTER TABLE live_transcode_task PARTITION BY RANGE (YEAR(create_time) * 100 + MONTH(create_time)) (
    PARTITION p202501 VALUES LESS THAN (202502),
    PARTITION p202502 VALUES LESS THAN (202503),
    -- ... 继续添加分区
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

## 3. 读写分离配置

### 查询路由规则
- **写操作**: 直播创建、状态更新、转码任务创建 -> 主库
- **读操作**: 直播列表查询、详情查询、统计查询 -> 从库
- **实时性要求高**: 直播状态查询 -> 主库
- **实时性要求低**: 历史数据查询 -> 从库

## 4. 缓存策略

### Redis 缓存设计
```
# 直播信息缓存
live:info:{liveId} -> LiveVo (TTL: 5分钟)

# 推流信息缓存
live:stream:{liveId} -> LiveStreamInfo (TTL: 10分钟)

# 直播状态缓存
live:status:{liveId} -> LiveStatus (TTL: 1分钟)

# 热门直播列表缓存
live:hot:list -> List<LiveVo> (TTL: 2分钟)

# 用户直播列表缓存
live:user:{userId}:list -> List<LiveVo> (TTL: 5分钟)

# 转码任务状态缓存
transcode:task:{bizId} -> TaskStatus (TTL: 30分钟)
```

## 5. 数据归档策略

### 冷热数据分离
```sql
-- 创建历史数据表
CREATE TABLE live_history LIKE live;
CREATE TABLE live_transcode_task_history LIKE live_transcode_task;

-- 定期归档脚本（归档3个月前的数据）
INSERT INTO live_history 
SELECT * FROM live 
WHERE create_time < DATE_SUB(NOW(), INTERVAL 3 MONTH);

DELETE FROM live 
WHERE create_time < DATE_SUB(NOW(), INTERVAL 3 MONTH);
```

## 6. 性能监控

### 慢查询监控
```sql
-- 开启慢查询日志
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 2;

-- 监控关键查询
-- 1. 直播列表分页查询
-- 2. 直播详情查询（包含推流信息）
-- 3. 转码任务状态查询
-- 4. 用户直播历史查询
```

### 关键指标监控
- 数据库连接数
- 查询响应时间
- 索引使用率
- 缓存命中率
- 主从延迟时间

## 7. 数据一致性保证

### 分布式事务
```java
// 使用 @Transactional 保证本地事务
@Transactional(rollbackFor = Exception.class)
public void createLiveWithStream(LiveBo bo) {
    // 1. 创建直播
    liveService.insertByBo(bo);
    
    // 2. 创建推流信息
    liveStreamService.createStreamForLive(bo.getId(), bo.getTitle());
    
    // 3. 初始化状态
    stateManager.initializeLiveState(bo.getId());
}
```

### 最终一致性
```java
// 使用事件驱动保证最终一致性
@EventListener
public void handleLiveCreated(LiveCreatedEvent event) {
    // 异步处理：发送通知、更新统计等
    asyncTaskExecutor.execute(() -> {
        notificationService.sendLiveCreatedNotification(event.getLiveId());
        statisticsService.updateLiveCount(event.getCategoryId());
    });
}
```

## 8. 备份策略

### 定期备份
```bash
# 每日全量备份
mysqldump --single-transaction --routines --triggers \
  --databases live_db > backup_$(date +%Y%m%d).sql

# 每小时增量备份（基于binlog）
mysqlbinlog --start-datetime="2025-01-01 00:00:00" \
  --stop-datetime="2025-01-01 01:00:00" \
  mysql-bin.000001 > incremental_backup.sql
```

### 异地备份
- 主备份：本地存储
- 异地备份：云存储（OSS/S3）
- 备份验证：定期恢复测试
